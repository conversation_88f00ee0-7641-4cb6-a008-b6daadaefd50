import React, { useState, useEffect } from 'react';
import {
    Box,
    Container,
    Fade
} from '@mui/material';
import {
    ViewList as ViewListIcon
} from '@mui/icons-material';

// Common Components
import TicketPageHeader from './common/TicketPageHeader';
import DashboardStats from './common/DashboardStats';
import DataTableCard from './common/DataTableCard';
import { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';
// import DatePicker from 'react-datepicker';
// import "react-datepicker/dist/react-datepicker.css";
import '../styles/main.scss';
import * as XLSX from 'xlsx';
import alasql from 'alasql';
import { formatDate } from '../services/CommonHelper';

const AllTickets = () => {
    const [stats, setStats] = useState({
        NEWCASE: 0,
        OPENCASE: 0,
        TATCASE: 0,
        Resolved: 0,
        Closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(4);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    data.unshift({ Name: "Select", SourceID: 0 });
                    setSource(data);
                    if (userDetails?.EMPData[0]?.ProcessID > 0) {
                        setSelected(prev => ({
                            ...prev,
                            Source: { SourceID: userDetails.EMPData[0].ProcessID }
                        }));
                    }
                }
            })
            .catch(() => {
                setSource([]);
            });
    };

    const GetDashboardCount = (_type) => {
        const objRequest = {
            type: _type,
        };

        GetSalesTicketCount(objRequest)
            .then((data) => {
                if (data.length > 0) {
                    data.forEach(item => {
                        switch (item.StatusID) {
                            case 1:
                                setStats(prev => ({ ...prev, NEWCASE: item.Count }));
                                break;
                            case 2:
                                setStats(prev => ({ ...prev, OPENCASE: item.Count }));
                                break;
                            case 3:
                                setStats(prev => ({ ...prev, Resolved: item.Count }));
                                break;
                            case 4:
                                setStats(prev => ({ ...prev, Closed: item.Count }));
                                break;
                            case 5:
                                setStats(prev => ({ ...prev, TATCASE: item.Count }));
                                break;
                            default:
                                break;
                        }
                    });
                }
            })
            .catch(() => {
                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
            });
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    setIssueSubIssue(data);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
            .then((data) => {
                if (data && data.length > 0) {
                    setStatusList(data);
                }
            })
            .catch(() => {
                setStatusList([]);
            });
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;

        var fromDateStr = formatDateForRequest(fromDate,3);
        var toDateStr = formatDateForRequest(toDate,0);

        if(status === 8){
            fromDateStr = formatDateForRequest(fromDate,0);
            toDateStr = formatDateForRequest(toDate,0);
        } 

        const obj = {
            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,
            FromDate: fromDateStr,
            ToDate: toDateStr,
            ProcessID: selected.Source?.SourceID || 0,
            IssueID: selected.IssueType?.IssueID || 0,
            StatusID: statusId,
            TicketID: 0,
            TicketDisplayID: ticketId?.trim() || "",
            ProductID: selected.Product ? selected.Product.ProductID : 0,
            FeedBackTypeID: 4
        };

        GetAdminTicketList(obj)
            .then((data) => {
                if (data && data.length > 0) {
                    const sortedFeedbacks = [...data].sort((a, b) => 
                        new Date(b.CreatedOn) - new Date(a.CreatedOn)
                    );
                    setFeedbacks(sortedFeedbacks);
                } else {
                    setFeedbacks([]);
                }
            })
            .catch(() => {
                setFeedbacks([]);
            });
    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const exportData = () => {

        if (typeof window !== 'undefined') {
            window.XLSX = XLSX;
        }

        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';
            
            return formatDate(dateStr);
        };
        
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,'
            + 'CreatedByDetails -> EmployeeID as EmpID,'
            + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,'
            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'
            + ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ', [feedbacks]
        );
    };

    const resetFilters = () => {
        setSelected({
            Source: { SourceID: 0, Name: 'Select' },
            IssueType: undefined,
            Status: undefined,
            Product: { ProductID: 0, Name: 'Select' }
        });
        setTicketId('');
        setFromDate(new Date());
        setToDate(new Date());
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE || 0, id: 1, color: '#4facfe', className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE || 0, id: 2, color: '#fcb69f', className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5, color: '#ff9a9e', className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved || 0, id: 3, color: '#a8edea', className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed || 0, id: 4, color: '#667eea', className: 'closed-status' }
    ];

    return (
        <Box className="assigned-tickets-main">
            <Container maxWidth="xl" className="assigned-tickets-container">
                <Fade in timeout={800}>
                    <Box>
                        {/* Header and Search Form */}
                        <TicketPageHeader
                            title="All Tickets"
                            subtitle="View and manage all feedback tickets across the system"
                            icon={ViewListIcon}
                            activeSearchType={activeSearchType}
                            setActiveSearchType={setActiveSearchType}
                            resetFilters={resetFilters}
                            fromDate={fromDate}
                            setFromDate={setFromDate}
                            toDate={toDate}
                            setToDate={setToDate}
                            selected={selected}
                            setSelected={setSelected}
                            ticketId={ticketId}
                            setTicketId={setTicketId}
                            source={source}
                            issueSubIssue={issueSubIssue}
                            statusList={statusList}
                            ProductOptions={ProductOptions}
                            onSearch={() => GetAgentTicketList(8)}
                            showProductField={true}
                            searchButtonText="Search Tickets"
                        />

                        {/* Dashboard Stats */}
                        {activeSearchType === 2 && (
                            <DashboardStats
                                statCards={statCards}
                                onStatClick={GetAgentTicketList}
                            />
                        )}

                        {/* Data Table */}
                        <DataTableCard
                            feedbacks={feedbacks}
                            onExport={exportData}
                            tableType={5}
                            redirectPage="/TicketDetails/"
                            tableTitle="All Ticket Results"
                        />
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default AllTickets;