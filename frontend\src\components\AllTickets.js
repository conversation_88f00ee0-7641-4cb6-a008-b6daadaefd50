import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Container,
    Grid2 as Grid,
    Typography,
    TextField,
    MenuItem,
    InputLabel,
    Select,
    FormControl,
    Paper,
    Fade,
    Grow,
    Stack,
    Chip,
    IconButton,
    Tooltip
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    Search as SearchIcon,
    ViewList as ViewListIcon,
    GetApp as GetAppIcon,
    FilterList as FilterListIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import FeedbackTable from './FeedbackTable';
import { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';
// import DatePicker from 'react-datepicker';
// import "react-datepicker/dist/react-datepicker.css";
import '../styles/MyFeedback.css';
import '../styles/FeedbackStats.css';
import '../styles/MyAssignedTickets.css';
import * as XLSX from 'xlsx';
import alasql from 'alasql';
import { formatDate } from '../services/CommonHelper';

const AllTickets = () => {
    const [stats, setStats] = useState({
        NEWCASE: 0,
        OPENCASE: 0,
        TATCASE: 0,
        Resolved: 0,
        Closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(4);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    data.unshift({ Name: "Select", SourceID: 0 });
                    setSource(data);
                    if (userDetails?.EMPData[0]?.ProcessID > 0) {
                        setSelected(prev => ({
                            ...prev,
                            Source: { SourceID: userDetails.EMPData[0].ProcessID }
                        }));
                    }
                }
            })
            .catch(() => {
                setSource([]);
            });
    };

    const GetDashboardCount = (_type) => {
        const objRequest = {
            type: _type,
        };

        GetSalesTicketCount(objRequest)
            .then((data) => {
                if (data.length > 0) {
                    data.forEach(item => {
                        switch (item.StatusID) {
                            case 1:
                                setStats(prev => ({ ...prev, NEWCASE: item.Count }));
                                break;
                            case 2:
                                setStats(prev => ({ ...prev, OPENCASE: item.Count }));
                                break;
                            case 3:
                                setStats(prev => ({ ...prev, Resolved: item.Count }));
                                break;
                            case 4:
                                setStats(prev => ({ ...prev, Closed: item.Count }));
                                break;
                            case 5:
                                setStats(prev => ({ ...prev, TATCASE: item.Count }));
                                break;
                            default:
                                break;
                        }
                    });
                }
            })
            .catch(() => {
                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
            });
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    setIssueSubIssue(data);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
            .then((data) => {
                if (data && data.length > 0) {
                    setStatusList(data);
                }
            })
            .catch(() => {
                setStatusList([]);
            });
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;

        var fromDateStr = formatDateForRequest(fromDate,3);
        var toDateStr = formatDateForRequest(toDate,0);

        if(status === 8){
            fromDateStr = formatDateForRequest(fromDate,0);
            toDateStr = formatDateForRequest(toDate,0);
        } 

        const obj = {
            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,
            FromDate: fromDateStr,
            ToDate: toDateStr,
            ProcessID: selected.Source?.SourceID || 0,
            IssueID: selected.IssueType?.IssueID || 0,
            StatusID: statusId,
            TicketID: 0,
            TicketDisplayID: ticketId?.trim() || "",
            ProductID: selected.Product ? selected.Product.ProductID : 0,
            FeedBackTypeID: 4
        };

        GetAdminTicketList(obj)
            .then((data) => {
                if (data && data.length > 0) {
                    const sortedFeedbacks = [...data].sort((a, b) => 
                        new Date(b.CreatedOn) - new Date(a.CreatedOn)
                    );
                    setFeedbacks(sortedFeedbacks);
                } else {
                    setFeedbacks([]);
                }
            })
            .catch(() => {
                setFeedbacks([]);
            });
    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const exportData = () => {

        if (typeof window !== 'undefined') {
            window.XLSX = XLSX;
        }

        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';
            
            return formatDate(dateStr);
        };
        
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,'
            + 'CreatedByDetails -> EmployeeID as EmpID,'
            + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,'
            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'
            + ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ', [feedbacks]
        );
    };

    const resetFilters = () => {
        setSelected({
            Source: { SourceID: 0, Name: 'Select' },
            IssueType: undefined,
            Status: undefined,
            Product: { ProductID: 0, Name: 'Select' }
        });
        setTicketId('');
        setFromDate(new Date());
        setToDate(new Date());
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE || 0, id: 1, color: '#4facfe', className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE || 0, id: 2, color: '#fcb69f', className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5, color: '#ff9a9e', className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved || 0, id: 3, color: '#a8edea', className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed || 0, id: 4, color: '#667eea', className: 'closed-status' }
    ];

    return (
        <Box className="page-container">
            <Container maxWidth="xl" className="main-container">
                <Fade in timeout={800}>
                    <Box>
                        {/* Header Section */}
                        <Paper
                            elevation={0}
                            className="header-paper"
                        >
                            <Grid container spacing={2} alignItems="center" className="header-content">
                                <Grid size={{ xs: 12, md: 8 }}>
                                    <Stack direction="row" spacing={2} alignItems="center">
                                        <ViewListIcon className="header-icon" />
                                        <Box>
                                            <Typography variant="h4" className="header-title">
                                                All Tickets
                                            </Typography>
                                            <Typography variant="body1" className="header-subtitle">
                                                View and manage all feedback tickets across the system
                                            </Typography>
                                        </Box>
                                    </Stack>
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <Stack direction="row" spacing={2} justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                                        <Button
                                            variant={activeSearchType === 2 ? "contained" : "outlined"}
                                            startIcon={<DashboardIcon />}
                                            onClick={() => {
                                                setActiveSearchType(2);
                                                resetFilters();
                                            }}
                                            className={`header-btn ${activeSearchType === 2 ? 'active' : ''}`}
                                        >
                                            Dashboard
                                        </Button>
                                        <Button
                                            variant={activeSearchType === 1 ? "contained" : "outlined"}
                                            startIcon={<SearchIcon />}
                                            onClick={() => setActiveSearchType(1)}
                                            className={`header-btn ${activeSearchType === 1 ? 'active' : ''}`}
                                        >
                                            Search
                                        </Button>
                                    </Stack>
                                </Grid>
                            </Grid>
                        </Paper>

                        {/* Dashboard Stats */}
                        {activeSearchType === 2 && (

                               <div className="feedback-stats">
                               {statCards.map((stat) => (
                                   <div
                                       key={stat.label}
                                       className={`stat-card ${stat.className}`}
                                       style={{ backgroundColor: stat.color }}
                                       onClick={() => GetAgentTicketList(stat.id)}
                                   >
                                       <h2>{stat.count}</h2>
                                       <p>{stat.label}</p>
                                   </div>
                               ))}
                           </div>
                        )}

                        {/* Search Form */}
                        {activeSearchType === 1 && (
                            <Grow in timeout={1000}>
                                <Card
                                    elevation={0}
                                    className="search-form-card"
                                >
                                    <CardContent className="search-card-content">
                                        <Stack direction="row" spacing={2} alignItems="center" className="search-header">
                                            <FilterListIcon className="search-icon" />
                                            <Typography variant="h6" className="search-title">
                                                Advanced Search Filters
                                            </Typography>
                                            <Tooltip title="Refresh Filters">
                                                <IconButton
                                                    onClick={resetFilters}
                                                    className="refresh-btn"
                                                    size="small"
                                                >
                                                    <RefreshIcon />
                                                </IconButton>
                                            </Tooltip>
                                        </Stack>

                                        <Grid container spacing={3}>
                                            {/* Date Range */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <TextField
                                                    label="From Date"
                                                    type="date"
                                                    fullWidth
                                                    value={fromDate.toISOString().split('T')[0]}
                                                    onChange={(e) => setFromDate(new Date(e.target.value))}
                                                    InputLabelProps={{ shrink: true }}
                                                    className="form-field"
                                                />
                                            </Grid>

                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <TextField
                                                    label="To Date"
                                                    type="date"
                                                    fullWidth
                                                    value={toDate.toISOString().split('T')[0]}
                                                    onChange={(e) => setToDate(new Date(e.target.value))}
                                                    InputLabelProps={{ shrink: true }}
                                                    className="form-field"
                                                />
                                            </Grid>
                                            {/* Process */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth className="form-field">
                                                    <InputLabel>Process</InputLabel>
                                                    <Select
                                                        label="Process"
                                                        value={selected.Source?.SourceID || 0}
                                                        onChange={(e) => setSelected(prev => ({
                                                            ...prev,
                                                            Source: { SourceID: parseInt(e.target.value) }
                                                        }))}
                                                    >
                                                        {source.map(s => (
                                                            <MenuItem key={s.SourceID} value={s.SourceID}>
                                                                {s.Name}
                                                            </MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Product */}
                                            {selected.Source?.SourceID && [2, 4, 5, 8].includes(selected.Source?.SourceID) && (
                                                <Grid size={{ xs: 12, md: 3 }}>
                                                    <FormControl fullWidth className="form-field">
                                                        <InputLabel>Product</InputLabel>
                                                        <Select
                                                            label="Product"
                                                            value={selected.Product?.ProductID || 0}
                                                            onChange={(e) => setSelected(prev => ({
                                                                ...prev,
                                                                Product: { ProductID: parseInt(e.target.value) }
                                                            }))}
                                                        >
                                                            {ProductOptions.map(p => (
                                                                <MenuItem key={p.ProductID} value={p.ProductID}>
                                                                    {p.Name}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                    </FormControl>
                                                </Grid>
                                            )}

                                            {/* Feedback */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth className="form-field">
                                                    <InputLabel>Feedback</InputLabel>
                                                    <Select
                                                        label="Feedback"
                                                        value={selected.IssueType?.IssueID || ''}
                                                        onChange={(e) => setSelected(prev => ({
                                                            ...prev,
                                                            IssueType: { IssueID: parseInt(e.target.value) }
                                                        }))}
                                                    >
                                                        <MenuItem value="">Select Feedback</MenuItem>
                                                        {issueSubIssue
                                                            .filter(item => item.SourceID === selected.Source?.SourceID)
                                                            .map(issue => (
                                                                <MenuItem key={issue.IssueID} value={issue.IssueID}>
                                                                    {issue.ISSUENAME}
                                                                </MenuItem>
                                                            ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Status */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth className="form-field">
                                                    <InputLabel>Status</InputLabel>
                                                    <Select
                                                        label="Status"
                                                        value={selected.Status?.StatusID || ''}
                                                        onChange={(e) => setSelected(prev => ({
                                                            ...prev,
                                                            Status: { StatusID: parseInt(e.target.value) }
                                                        }))}
                                                    >
                                                        <MenuItem value="">Select Status</MenuItem>
                                                        {statusList.map(status => (
                                                            <MenuItem key={status.StatusID} value={status.StatusID}>
                                                                {status.StatusName}
                                                            </MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Feedback ID */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <TextField
                                                    label="Feedback ID"
                                                    fullWidth
                                                    value={ticketId}
                                                    onChange={(e) => setTicketId(e.target.value)}
                                                    placeholder="Enter Feedback ID"
                                                    className="form-field"
                                                />
                                            </Grid>

                                            {/* Search Button */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <Stack direction="row" spacing={2} className="action-buttons">
                                                    <Button
                                                        variant="contained"
                                                        startIcon={<SearchIcon />}
                                                        onClick={() => GetAgentTicketList(8)}
                                                        className="search-btn"
                                                        fullWidth
                                                    >
                                                        Search
                                                    </Button>
                                                </Stack>
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grow>
                        )}

                        {/* Data Table */}
                        <Grow in timeout={1200}>
                            <Card
                                elevation={0}
                                className="data-table-card"
                            >
                                <CardContent className="table-card-content">
                                    {feedbacks.length > 0 && (
                                        <Box className="table-header">
                                            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                                                <Stack direction="row" spacing={2} alignItems="center">
                                                    <Typography variant="h6" className="table-title">
                                                        Ticket Results
                                                    </Typography>
                                                    <Chip
                                                        label={`${feedbacks.length} tickets`}
                                                        size="small"
                                                        className="table-count-chip"
                                                    />
                                                </Stack>
                                                <Button
                                                    variant="outlined"
                                                    startIcon={<GetAppIcon />}
                                                    onClick={exportData}
                                                    className="export-btn"
                                                >
                                                    Export Data
                                                </Button>
                                            </Stack>
                                        </Box>
                                    )}
                                    <Box className="table-content">
                                        <FeedbackTable feedbacks={feedbacks} type={5} redirectPage='/TicketDetails/' />
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grow>
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default AllTickets;