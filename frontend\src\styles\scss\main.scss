/* Main SCSS Entry Point */

// Import all SCSS modules
@import './layout.scss';
@import './header-search.scss';
@import './data-table.scss';

/* Additional Global Styles */

// Global Reset and Base Styles
* {
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  margin: 0;
  padding: 0;
  background-color: $background-color;
  color: $text-primary;
  line-height: 1.6;
}

// Utility Classes
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

// Color Utilities
.text-primary { color: $primary-color !important; }
.text-secondary { color: $text-secondary !important; }
.text-success { color: $success-color !important; }
.text-info { color: $info-color !important; }
.text-warning { color: $warning-color !important; }
.text-error { color: $error-color !important; }

.bg-primary { background-color: $primary-color !important; }
.bg-secondary { background-color: $text-secondary !important; }
.bg-success { background-color: $success-color !important; }
.bg-info { background-color: $info-color !important; }
.bg-warning { background-color: $warning-color !important; }
.bg-error { background-color: $error-color !important; }

// Animation Classes
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

// Print Styles
@media print {
  .no-print {
    display: none !important;
  }
  
  .assigned-tickets-main {
    background-color: white !important;
    padding: 0 !important;
  }
  
  .tickets-header {
    background: white !important;
    color: black !important;
    border: 1px solid #ccc !important;
  }
  
  .search-form-card {
    display: none !important;
  }
}
