[{"D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js": "1", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js": "2", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js": "3", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js": "4", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js": "5", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js": "6", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js": "7", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js": "8", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js": "9", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js": "10", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js": "11", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js": "12", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js": "13", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js": "14", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js": "15", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js": "16", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js": "17", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js": "18", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js": "19", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js": "20", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js": "21", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js": "22"}, {"size": 661, "mtime": 1750674074277, "results": "23", "hashOfConfig": "24"}, {"size": 1127, "mtime": 1751266713093, "results": "25", "hashOfConfig": "24"}, {"size": 345, "mtime": 1750146475108, "results": "26", "hashOfConfig": "24"}, {"size": 2588, "mtime": 1750146475047, "results": "27", "hashOfConfig": "24"}, {"size": 6438, "mtime": 1750759167832, "results": "28", "hashOfConfig": "24"}, {"size": 30204, "mtime": 1750403903150, "results": "29", "hashOfConfig": "24"}, {"size": 30846, "mtime": 1751264940370, "results": "30", "hashOfConfig": "24"}, {"size": 24970, "mtime": 1751276945569, "results": "31", "hashOfConfig": "24"}, {"size": 9277, "mtime": 1751276847574, "results": "32", "hashOfConfig": "24"}, {"size": 3713, "mtime": 1751274594344, "results": "33", "hashOfConfig": "24"}, {"size": 21384, "mtime": 1751274594332, "results": "34", "hashOfConfig": "24"}, {"size": 4962, "mtime": 1750146474877, "results": "35", "hashOfConfig": "24"}, {"size": 24393, "mtime": 1751276252940, "results": "36", "hashOfConfig": "24"}, {"size": 39421, "mtime": 1750403903312, "results": "37", "hashOfConfig": "24"}, {"size": 3837, "mtime": 1750403903532, "results": "38", "hashOfConfig": "24"}, {"size": 2848, "mtime": 1750146476432, "results": "39", "hashOfConfig": "24"}, {"size": 4473, "mtime": 1750422157245, "results": "40", "hashOfConfig": "24"}, {"size": 2417, "mtime": 1750654173845, "results": "41", "hashOfConfig": "24"}, {"size": 688, "mtime": 1750423596388, "results": "42", "hashOfConfig": "24"}, {"size": 2457, "mtime": 1751276612112, "results": "43", "hashOfConfig": "24"}, {"size": 706, "mtime": 1751276597623, "results": "44", "hashOfConfig": "24"}, {"size": 13084, "mtime": 1751276583193, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s4k03p", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 52, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js", ["112", "113", "114"], ["115", "116", "117", "118", "119", "120", "121", "122"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js", ["123", "124", "125", "126"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js", ["127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js", ["181"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js", ["182", "183", "184", "185", "186", "187", "188", "189", "190", "191"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js", ["192", "193", "194", "195"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js", ["196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207"], ["208", "209", "210", "211", "212"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js", ["213", "214"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js", ["215"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js", [], [], {"ruleId": "216", "severity": 1, "message": "217", "line": 31, "column": 40, "nodeType": "218", "messageId": "219", "endLine": 31, "endColumn": 42}, {"ruleId": "216", "severity": 1, "message": "217", "line": 31, "column": 55, "nodeType": "218", "messageId": "219", "endLine": 31, "endColumn": 57}, {"ruleId": "220", "severity": 1, "message": "221", "line": 82, "column": 8, "nodeType": "222", "endLine": 82, "endColumn": 18, "suggestions": "223"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 45, "column": 43, "nodeType": "226", "messageId": "227", "endLine": 45, "endColumn": 45, "suppressions": "228"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 256, "column": 100, "nodeType": "226", "messageId": "227", "endLine": 256, "endColumn": 102, "suppressions": "229"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 256, "column": 126, "nodeType": "226", "messageId": "227", "endLine": 256, "endColumn": 128, "suppressions": "230"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 261, "column": 104, "nodeType": "233", "endLine": 261, "endColumn": 107, "suppressions": "234"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 269, "column": 101, "nodeType": "233", "endLine": 271, "endColumn": 268, "suppressions": "235"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 276, "column": 101, "nodeType": "233", "endLine": 279, "endColumn": 102, "suppressions": "236"}, {"ruleId": "224", "severity": 1, "message": "237", "line": 359, "column": 119, "nodeType": "226", "messageId": "227", "endLine": 359, "endColumn": 121, "suppressions": "238"}, {"ruleId": "224", "severity": 1, "message": "237", "line": 391, "column": 119, "nodeType": "226", "messageId": "227", "endLine": 391, "endColumn": 121, "suppressions": "239"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 16, "column": 5, "nodeType": "242", "messageId": "243", "endLine": 16, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "244", "line": 42, "column": 50, "nodeType": "242", "messageId": "243", "endLine": 42, "endColumn": 56}, {"ruleId": "220", "severity": 1, "message": "245", "line": 62, "column": 8, "nodeType": "222", "endLine": 62, "endColumn": 10, "suggestions": "246"}, {"ruleId": "224", "severity": 1, "message": "237", "line": 152, "column": 27, "nodeType": "226", "messageId": "227", "endLine": 152, "endColumn": 29}, {"ruleId": "240", "severity": 1, "message": "247", "line": 14, "column": 8, "nodeType": "242", "messageId": "243", "endLine": 14, "endColumn": 21}, {"ruleId": "220", "severity": 1, "message": "245", "line": 64, "column": 8, "nodeType": "222", "endLine": 64, "endColumn": 10, "suggestions": "248"}, {"ruleId": "249", "severity": 2, "message": "250", "line": 274, "column": 30, "nodeType": "251", "messageId": "252", "endLine": 274, "endColumn": 34}, {"ruleId": "249", "severity": 2, "message": "253", "line": 275, "column": 34, "nodeType": "251", "messageId": "252", "endLine": 275, "endColumn": 38}, {"ruleId": "249", "severity": 2, "message": "254", "line": 279, "column": 38, "nodeType": "251", "messageId": "252", "endLine": 279, "endColumn": 49}, {"ruleId": "249", "severity": 2, "message": "255", "line": 280, "column": 42, "nodeType": "251", "messageId": "252", "endLine": 280, "endColumn": 47}, {"ruleId": "249", "severity": 2, "message": "256", "line": 281, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 281, "endColumn": 60}, {"ruleId": "249", "severity": 2, "message": "257", "line": 282, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 282, "endColumn": 56}, {"ruleId": "249", "severity": 2, "message": "258", "line": 285, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 285, "endColumn": 53}, {"ruleId": "249", "severity": 2, "message": "259", "line": 286, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 286, "endColumn": 60}, {"ruleId": "249", "severity": 2, "message": "260", "line": 291, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 291, "endColumn": 65}, {"ruleId": "249", "severity": 2, "message": "261", "line": 296, "column": 42, "nodeType": "251", "messageId": "252", "endLine": 296, "endColumn": 46}, {"ruleId": "249", "severity": 2, "message": "261", "line": 298, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 298, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "262", "line": 299, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 299, "endColumn": 59}, {"ruleId": "249", "severity": 2, "message": "261", "line": 310, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 310, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "262", "line": 311, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 311, "endColumn": 59}, {"ruleId": "249", "severity": 2, "message": "261", "line": 322, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 322, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "263", "line": 323, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 323, "endColumn": 61}, {"ruleId": "249", "severity": 2, "message": "264", "line": 324, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 324, "endColumn": 64}, {"ruleId": "249", "severity": 2, "message": "265", "line": 325, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 325, "endColumn": 60}, {"ruleId": "249", "severity": 2, "message": "266", "line": 334, "column": 62, "nodeType": "251", "messageId": "252", "endLine": 334, "endColumn": 70}, {"ruleId": "249", "severity": 2, "message": "261", "line": 344, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 344, "endColumn": 54}, {"ruleId": "249", "severity": 2, "message": "263", "line": 345, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 345, "endColumn": 65}, {"ruleId": "249", "severity": 2, "message": "264", "line": 346, "column": 58, "nodeType": "251", "messageId": "252", "endLine": 346, "endColumn": 68}, {"ruleId": "249", "severity": 2, "message": "265", "line": 347, "column": 58, "nodeType": "251", "messageId": "252", "endLine": 347, "endColumn": 64}, {"ruleId": "249", "severity": 2, "message": "266", "line": 356, "column": 66, "nodeType": "251", "messageId": "252", "endLine": 356, "endColumn": 74}, {"ruleId": "249", "severity": 2, "message": "261", "line": 366, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 366, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "263", "line": 367, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 367, "endColumn": 61}, {"ruleId": "249", "severity": 2, "message": "264", "line": 368, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 368, "endColumn": 64}, {"ruleId": "249", "severity": 2, "message": "265", "line": 369, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 369, "endColumn": 60}, {"ruleId": "249", "severity": 2, "message": "266", "line": 377, "column": 58, "nodeType": "251", "messageId": "252", "endLine": 377, "endColumn": 66}, {"ruleId": "249", "severity": 2, "message": "266", "line": 381, "column": 66, "nodeType": "251", "messageId": "252", "endLine": 381, "endColumn": 74}, {"ruleId": "249", "severity": 2, "message": "261", "line": 390, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 390, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "263", "line": 391, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 391, "endColumn": 61}, {"ruleId": "249", "severity": 2, "message": "264", "line": 392, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 392, "endColumn": 64}, {"ruleId": "249", "severity": 2, "message": "265", "line": 393, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 393, "endColumn": 60}, {"ruleId": "249", "severity": 2, "message": "266", "line": 401, "column": 58, "nodeType": "251", "messageId": "252", "endLine": 401, "endColumn": 66}, {"ruleId": "249", "severity": 2, "message": "266", "line": 403, "column": 62, "nodeType": "251", "messageId": "252", "endLine": 403, "endColumn": 70}, {"ruleId": "249", "severity": 2, "message": "261", "line": 412, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 412, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "262", "line": 413, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 413, "endColumn": 59}, {"ruleId": "249", "severity": 2, "message": "261", "line": 424, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 424, "endColumn": 50}, {"ruleId": "249", "severity": 2, "message": "255", "line": 425, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 425, "endColumn": 55}, {"ruleId": "249", "severity": 2, "message": "267", "line": 426, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 426, "endColumn": 60}, {"ruleId": "249", "severity": 2, "message": "268", "line": 428, "column": 69, "nodeType": "251", "messageId": "252", "endLine": 428, "endColumn": 79}, {"ruleId": "249", "severity": 2, "message": "250", "line": 444, "column": 26, "nodeType": "251", "messageId": "252", "endLine": 444, "endColumn": 30}, {"ruleId": "249", "severity": 2, "message": "253", "line": 445, "column": 30, "nodeType": "251", "messageId": "252", "endLine": 445, "endColumn": 34}, {"ruleId": "249", "severity": 2, "message": "254", "line": 449, "column": 34, "nodeType": "251", "messageId": "252", "endLine": 449, "endColumn": 45}, {"ruleId": "249", "severity": 2, "message": "255", "line": 452, "column": 46, "nodeType": "251", "messageId": "252", "endLine": 452, "endColumn": 51}, {"ruleId": "249", "severity": 2, "message": "255", "line": 453, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 453, "endColumn": 55}, {"ruleId": "249", "severity": 2, "message": "257", "line": 454, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 454, "endColumn": 64}, {"ruleId": "249", "severity": 2, "message": "269", "line": 457, "column": 54, "nodeType": "251", "messageId": "252", "endLine": 457, "endColumn": 58}, {"ruleId": "249", "severity": 2, "message": "267", "line": 463, "column": 50, "nodeType": "251", "messageId": "252", "endLine": 463, "endColumn": 56}, {"ruleId": "249", "severity": 2, "message": "270", "line": 465, "column": 65, "nodeType": "251", "messageId": "252", "endLine": 465, "endColumn": 75}, {"ruleId": "249", "severity": 2, "message": "271", "line": 475, "column": 42, "nodeType": "251", "messageId": "252", "endLine": 475, "endColumn": 55}, {"ruleId": "220", "severity": 1, "message": "245", "line": 62, "column": 8, "nodeType": "222", "endLine": 62, "endColumn": 10, "suggestions": "272"}, {"ruleId": "240", "severity": 1, "message": "273", "line": 26, "column": 22, "nodeType": "242", "messageId": "243", "endLine": 26, "endColumn": 33}, {"ruleId": "240", "severity": 1, "message": "274", "line": 27, "column": 20, "nodeType": "242", "messageId": "243", "endLine": 27, "endColumn": 29}, {"ruleId": "220", "severity": 1, "message": "245", "line": 52, "column": 8, "nodeType": "222", "endLine": 52, "endColumn": 10, "suggestions": "275"}, {"ruleId": "224", "severity": 1, "message": "237", "line": 158, "column": 46, "nodeType": "226", "messageId": "227", "endLine": 158, "endColumn": 48}, {"ruleId": "224", "severity": 1, "message": "225", "line": 182, "column": 44, "nodeType": "226", "messageId": "227", "endLine": 182, "endColumn": 46}, {"ruleId": "224", "severity": 1, "message": "225", "line": 190, "column": 41, "nodeType": "226", "messageId": "227", "endLine": 190, "endColumn": 43}, {"ruleId": "224", "severity": 1, "message": "225", "line": 197, "column": 59, "nodeType": "226", "messageId": "227", "endLine": 197, "endColumn": 61}, {"ruleId": "224", "severity": 1, "message": "237", "line": 201, "column": 30, "nodeType": "226", "messageId": "227", "endLine": 201, "endColumn": 32}, {"ruleId": "224", "severity": 1, "message": "237", "line": 201, "column": 62, "nodeType": "226", "messageId": "227", "endLine": 201, "endColumn": 64}, {"ruleId": "224", "severity": 1, "message": "225", "line": 203, "column": 62, "nodeType": "226", "messageId": "227", "endLine": 203, "endColumn": 64}, {"ruleId": "240", "severity": 1, "message": "241", "line": 20, "column": 5, "nodeType": "242", "messageId": "243", "endLine": 20, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "276", "line": 21, "column": 5, "nodeType": "242", "messageId": "243", "endLine": 21, "endColumn": 12}, {"ruleId": "240", "severity": 1, "message": "277", "line": 28, "column": 18, "nodeType": "242", "messageId": "243", "endLine": 28, "endColumn": 31}, {"ruleId": "220", "severity": 1, "message": "245", "line": 73, "column": 8, "nodeType": "222", "endLine": 73, "endColumn": 10, "suggestions": "278"}, {"ruleId": "220", "severity": 1, "message": "221", "line": 37, "column": 8, "nodeType": "222", "endLine": 37, "endColumn": 18, "suggestions": "279"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 82, "column": 36, "nodeType": "226", "messageId": "227", "endLine": 82, "endColumn": 38}, {"ruleId": "224", "severity": 1, "message": "225", "line": 91, "column": 35, "nodeType": "226", "messageId": "227", "endLine": 91, "endColumn": 37}, {"ruleId": "224", "severity": 1, "message": "225", "line": 91, "column": 57, "nodeType": "226", "messageId": "227", "endLine": 91, "endColumn": 59}, {"ruleId": "224", "severity": 1, "message": "225", "line": 160, "column": 19, "nodeType": "226", "messageId": "227", "endLine": 160, "endColumn": 21}, {"ruleId": "224", "severity": 1, "message": "225", "line": 182, "column": 25, "nodeType": "226", "messageId": "227", "endLine": 182, "endColumn": 27}, {"ruleId": "224", "severity": 1, "message": "225", "line": 197, "column": 25, "nodeType": "226", "messageId": "227", "endLine": 197, "endColumn": 27}, {"ruleId": "224", "severity": 1, "message": "237", "line": 199, "column": 47, "nodeType": "226", "messageId": "227", "endLine": 199, "endColumn": 49}, {"ruleId": "224", "severity": 1, "message": "237", "line": 199, "column": 79, "nodeType": "226", "messageId": "227", "endLine": 199, "endColumn": 81}, {"ruleId": "224", "severity": 1, "message": "225", "line": 218, "column": 24, "nodeType": "226", "messageId": "227", "endLine": 218, "endColumn": 26}, {"ruleId": "224", "severity": 1, "message": "225", "line": 251, "column": 22, "nodeType": "226", "messageId": "227", "endLine": 251, "endColumn": 24}, {"ruleId": "224", "severity": 1, "message": "225", "line": 254, "column": 42, "nodeType": "226", "messageId": "227", "endLine": 254, "endColumn": 44}, {"ruleId": "231", "severity": 1, "message": "232", "line": 441, "column": 66, "nodeType": "233", "endLine": 441, "endColumn": 164, "suppressions": "280"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 442, "column": 66, "nodeType": "233", "endLine": 442, "endColumn": 164, "suppressions": "281"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 477, "column": 118, "nodeType": "233", "endLine": 477, "endColumn": 121, "suppressions": "282"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 484, "column": 101, "nodeType": "233", "endLine": 486, "endColumn": 256, "suppressions": "283"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 491, "column": 101, "nodeType": "233", "endLine": 494, "endColumn": 102, "suppressions": "284"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 10, "column": 32, "nodeType": "226", "messageId": "227", "endLine": 10, "endColumn": 34}, {"ruleId": "224", "severity": 1, "message": "225", "line": 11, "column": 35, "nodeType": "226", "messageId": "227", "endLine": 11, "endColumn": 37}, {"ruleId": "240", "severity": 1, "message": "285", "line": 2, "column": 39, "nodeType": "242", "messageId": "243", "endLine": 2, "endColumn": 52}, "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", "ArrayExpression", ["286"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["287"], ["288"], ["289"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["290"], ["291"], ["292"], "Expected '!==' and instead saw '!='.", ["293"], ["294"], "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'errors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", ["295"], "'DataTableCard' is defined but never used.", ["296"], "react/jsx-no-undef", "'Grow' is not defined.", "JSXIdentifier", "undefined", "'Card' is not defined.", "'CardContent' is not defined.", "'Stack' is not defined.", "'FilterListIcon' is not defined.", "'Typography' is not defined.", "'Tooltip' is not defined.", "'IconButton' is not defined.", "'RefreshIcon' is not defined.", "'Grid' is not defined.", "'TextField' is not defined.", "'FormControl' is not defined.", "'InputLabel' is not defined.", "'Select' is not defined.", "'MenuItem' is not defined.", "'Button' is not defined.", "'SearchIcon' is not defined.", "'Chip' is not defined.", "'GetAppIcon' is not defined.", "'FeedbackTable' is not defined.", ["297"], "'setFromDate' is assigned a value but never used.", "'setToDate' is assigned a value but never used.", ["298"], "'Tooltip' is defined but never used.", "'DateRangeIcon' is defined but never used.", ["299"], ["300"], ["301"], ["302"], ["303"], ["304"], ["305"], "'clearAuthData' is defined but never used.", {"desc": "306", "fix": "307"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"desc": "310", "fix": "311"}, {"desc": "310", "fix": "312"}, {"desc": "310", "fix": "313"}, {"desc": "310", "fix": "314"}, {"desc": "310", "fix": "315"}, {"desc": "306", "fix": "316"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, {"kind": "308", "justification": "309"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "317", "text": "318"}, "directive", "", "Update the dependencies array to be: [GetAllProcess]", {"range": "319", "text": "320"}, {"range": "321", "text": "320"}, {"range": "322", "text": "320"}, {"range": "323", "text": "320"}, {"range": "324", "text": "320"}, {"range": "325", "text": "318"}, [2987, 2997], "[getTicketDetailsService, ticketId]", [1687, 1689], "[GetAllProcess]", [2247, 2249], [2140, 2142], [1982, 1984], [2129, 2131], [1782, 1792]]