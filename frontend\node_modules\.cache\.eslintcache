[{"D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js": "1", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js": "2", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js": "3", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js": "4", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js": "5", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js": "6", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js": "7", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js": "8", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js": "9", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js": "10", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js": "11", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js": "12", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js": "13", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js": "14", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js": "15", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js": "16", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js": "17", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js": "18", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js": "19", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js": "20", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js": "21", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js": "22"}, {"size": 664, "mtime": 1751279478399, "results": "23", "hashOfConfig": "24"}, {"size": 1127, "mtime": 1751266713093, "results": "25", "hashOfConfig": "24"}, {"size": 345, "mtime": 1750146475108, "results": "26", "hashOfConfig": "24"}, {"size": 2588, "mtime": 1750146475047, "results": "27", "hashOfConfig": "24"}, {"size": 6438, "mtime": 1750759167832, "results": "28", "hashOfConfig": "24"}, {"size": 30204, "mtime": 1750403903150, "results": "29", "hashOfConfig": "24"}, {"size": 30837, "mtime": 1751279464620, "results": "30", "hashOfConfig": "24"}, {"size": 10959, "mtime": 1751280208192, "results": "31", "hashOfConfig": "24"}, {"size": 9361, "mtime": 1751280245515, "results": "32", "hashOfConfig": "24"}, {"size": 3670, "mtime": 1751279437203, "results": "33", "hashOfConfig": "24"}, {"size": 13153, "mtime": 1751280227664, "results": "34", "hashOfConfig": "24"}, {"size": 4962, "mtime": 1750146474877, "results": "35", "hashOfConfig": "24"}, {"size": 10289, "mtime": 1751281007756, "results": "36", "hashOfConfig": "24"}, {"size": 39421, "mtime": 1750403903312, "results": "37", "hashOfConfig": "24"}, {"size": 3837, "mtime": 1750403903532, "results": "38", "hashOfConfig": "24"}, {"size": 2848, "mtime": 1750146476432, "results": "39", "hashOfConfig": "24"}, {"size": 4473, "mtime": 1750422157245, "results": "40", "hashOfConfig": "24"}, {"size": 2417, "mtime": 1750654173845, "results": "41", "hashOfConfig": "24"}, {"size": 688, "mtime": 1750423596388, "results": "42", "hashOfConfig": "24"}, {"size": 2524, "mtime": 1751279372587, "results": "43", "hashOfConfig": "24"}, {"size": 695, "mtime": 1751279359756, "results": "44", "hashOfConfig": "24"}, {"size": 13666, "mtime": 1751280425733, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s4k03p", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js", ["112", "113", "114"], ["115", "116", "117", "118", "119", "120", "121", "122"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js", ["123", "124", "125", "126"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js", ["127"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js", ["128"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js", ["129", "130", "131", "132", "133", "134", "135", "136"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js", ["137"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js", ["138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149"], ["150", "151", "152", "153", "154"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js", ["155", "156"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js", ["157"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js", [], [], {"ruleId": "158", "severity": 1, "message": "159", "line": 31, "column": 40, "nodeType": "160", "messageId": "161", "endLine": 31, "endColumn": 42}, {"ruleId": "158", "severity": 1, "message": "159", "line": 31, "column": 55, "nodeType": "160", "messageId": "161", "endLine": 31, "endColumn": 57}, {"ruleId": "162", "severity": 1, "message": "163", "line": 82, "column": 8, "nodeType": "164", "endLine": 82, "endColumn": 18, "suggestions": "165"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 45, "column": 43, "nodeType": "168", "messageId": "169", "endLine": 45, "endColumn": 45, "suppressions": "170"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 256, "column": 100, "nodeType": "168", "messageId": "169", "endLine": 256, "endColumn": 102, "suppressions": "171"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 256, "column": 126, "nodeType": "168", "messageId": "169", "endLine": 256, "endColumn": 128, "suppressions": "172"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 261, "column": 104, "nodeType": "175", "endLine": 261, "endColumn": 107, "suppressions": "176"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 269, "column": 101, "nodeType": "175", "endLine": 271, "endColumn": 268, "suppressions": "177"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 276, "column": 101, "nodeType": "175", "endLine": 279, "endColumn": 102, "suppressions": "178"}, {"ruleId": "166", "severity": 1, "message": "179", "line": 359, "column": 119, "nodeType": "168", "messageId": "169", "endLine": 359, "endColumn": 121, "suppressions": "180"}, {"ruleId": "166", "severity": 1, "message": "179", "line": 391, "column": 119, "nodeType": "168", "messageId": "169", "endLine": 391, "endColumn": 121, "suppressions": "181"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 16, "column": 5, "nodeType": "184", "messageId": "185", "endLine": 16, "endColumn": 15}, {"ruleId": "182", "severity": 1, "message": "186", "line": 42, "column": 50, "nodeType": "184", "messageId": "185", "endLine": 42, "endColumn": 56}, {"ruleId": "162", "severity": 1, "message": "187", "line": 62, "column": 8, "nodeType": "164", "endLine": 62, "endColumn": 10, "suggestions": "188"}, {"ruleId": "166", "severity": 1, "message": "179", "line": 152, "column": 27, "nodeType": "168", "messageId": "169", "endLine": 152, "endColumn": 29}, {"ruleId": "162", "severity": 1, "message": "187", "line": 62, "column": 8, "nodeType": "164", "endLine": 62, "endColumn": 10, "suggestions": "189"}, {"ruleId": "162", "severity": 1, "message": "187", "line": 60, "column": 8, "nodeType": "164", "endLine": 60, "endColumn": 10, "suggestions": "190"}, {"ruleId": "162", "severity": 1, "message": "187", "line": 64, "column": 8, "nodeType": "164", "endLine": 64, "endColumn": 10, "suggestions": "191"}, {"ruleId": "166", "severity": 1, "message": "179", "line": 171, "column": 46, "nodeType": "168", "messageId": "169", "endLine": 171, "endColumn": 48}, {"ruleId": "166", "severity": 1, "message": "167", "line": 195, "column": 44, "nodeType": "168", "messageId": "169", "endLine": 195, "endColumn": 46}, {"ruleId": "166", "severity": 1, "message": "167", "line": 203, "column": 41, "nodeType": "168", "messageId": "169", "endLine": 203, "endColumn": 43}, {"ruleId": "166", "severity": 1, "message": "167", "line": 210, "column": 59, "nodeType": "168", "messageId": "169", "endLine": 210, "endColumn": 61}, {"ruleId": "166", "severity": 1, "message": "179", "line": 214, "column": 30, "nodeType": "168", "messageId": "169", "endLine": 214, "endColumn": 32}, {"ruleId": "166", "severity": 1, "message": "179", "line": 214, "column": 62, "nodeType": "168", "messageId": "169", "endLine": 214, "endColumn": 64}, {"ruleId": "166", "severity": 1, "message": "167", "line": 216, "column": 62, "nodeType": "168", "messageId": "169", "endLine": 216, "endColumn": 64}, {"ruleId": "162", "severity": 1, "message": "187", "line": 55, "column": 8, "nodeType": "164", "endLine": 55, "endColumn": 10, "suggestions": "192"}, {"ruleId": "162", "severity": 1, "message": "163", "line": 37, "column": 8, "nodeType": "164", "endLine": 37, "endColumn": 18, "suggestions": "193"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 82, "column": 36, "nodeType": "168", "messageId": "169", "endLine": 82, "endColumn": 38}, {"ruleId": "166", "severity": 1, "message": "167", "line": 91, "column": 35, "nodeType": "168", "messageId": "169", "endLine": 91, "endColumn": 37}, {"ruleId": "166", "severity": 1, "message": "167", "line": 91, "column": 57, "nodeType": "168", "messageId": "169", "endLine": 91, "endColumn": 59}, {"ruleId": "166", "severity": 1, "message": "167", "line": 160, "column": 19, "nodeType": "168", "messageId": "169", "endLine": 160, "endColumn": 21}, {"ruleId": "166", "severity": 1, "message": "167", "line": 182, "column": 25, "nodeType": "168", "messageId": "169", "endLine": 182, "endColumn": 27}, {"ruleId": "166", "severity": 1, "message": "167", "line": 197, "column": 25, "nodeType": "168", "messageId": "169", "endLine": 197, "endColumn": 27}, {"ruleId": "166", "severity": 1, "message": "179", "line": 199, "column": 47, "nodeType": "168", "messageId": "169", "endLine": 199, "endColumn": 49}, {"ruleId": "166", "severity": 1, "message": "179", "line": 199, "column": 79, "nodeType": "168", "messageId": "169", "endLine": 199, "endColumn": 81}, {"ruleId": "166", "severity": 1, "message": "167", "line": 218, "column": 24, "nodeType": "168", "messageId": "169", "endLine": 218, "endColumn": 26}, {"ruleId": "166", "severity": 1, "message": "167", "line": 251, "column": 22, "nodeType": "168", "messageId": "169", "endLine": 251, "endColumn": 24}, {"ruleId": "166", "severity": 1, "message": "167", "line": 254, "column": 42, "nodeType": "168", "messageId": "169", "endLine": 254, "endColumn": 44}, {"ruleId": "173", "severity": 1, "message": "174", "line": 441, "column": 66, "nodeType": "175", "endLine": 441, "endColumn": 164, "suppressions": "194"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 442, "column": 66, "nodeType": "175", "endLine": 442, "endColumn": 164, "suppressions": "195"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 477, "column": 118, "nodeType": "175", "endLine": 477, "endColumn": 121, "suppressions": "196"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 484, "column": 101, "nodeType": "175", "endLine": 486, "endColumn": 256, "suppressions": "197"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 491, "column": 101, "nodeType": "175", "endLine": 494, "endColumn": 102, "suppressions": "198"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 10, "column": 32, "nodeType": "168", "messageId": "169", "endLine": 10, "endColumn": 34}, {"ruleId": "166", "severity": 1, "message": "167", "line": 11, "column": 35, "nodeType": "168", "messageId": "169", "endLine": 11, "endColumn": 37}, {"ruleId": "182", "severity": 1, "message": "199", "line": 2, "column": 39, "nodeType": "184", "messageId": "185", "endLine": 2, "endColumn": 52}, "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", "ArrayExpression", ["200"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["201"], ["202"], ["203"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["204"], ["205"], ["206"], "Expected '!==' and instead saw '!='.", ["207"], ["208"], "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'errors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", ["209"], ["210"], ["211"], ["212"], ["213"], ["214"], ["215"], ["216"], ["217"], ["218"], ["219"], "'clearAuthData' is defined but never used.", {"desc": "220", "fix": "221"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"desc": "224", "fix": "225"}, {"desc": "224", "fix": "226"}, {"desc": "224", "fix": "227"}, {"desc": "224", "fix": "228"}, {"desc": "224", "fix": "229"}, {"desc": "220", "fix": "230"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, {"kind": "222", "justification": "223"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "231", "text": "232"}, "directive", "", "Update the dependencies array to be: [GetAllProcess]", {"range": "233", "text": "234"}, {"range": "235", "text": "234"}, {"range": "236", "text": "234"}, {"range": "237", "text": "234"}, {"range": "238", "text": "234"}, {"range": "239", "text": "232"}, [2987, 2997], "[getTicketDetailsService, ticketId]", [1678, 1680], "[GetAllProcess]", [2160, 2162], [2053, 2055], [2233, 2235], [1884, 1886], [1782, 1792]]