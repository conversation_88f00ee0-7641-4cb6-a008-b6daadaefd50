[{"D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js": "1", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js": "2", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js": "3", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js": "4", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js": "5", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js": "6", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js": "7", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js": "8", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js": "9", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js": "10", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js": "11", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js": "12", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js": "13", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js": "14", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js": "15", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js": "16", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js": "17", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js": "18", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js": "19"}, {"size": 661, "mtime": 1750674074277, "results": "20", "hashOfConfig": "21"}, {"size": 1127, "mtime": 1751266713093, "results": "22", "hashOfConfig": "21"}, {"size": 345, "mtime": 1750146475108, "results": "23", "hashOfConfig": "21"}, {"size": 2588, "mtime": 1750146475047, "results": "24", "hashOfConfig": "21"}, {"size": 6438, "mtime": 1750759167832, "results": "25", "hashOfConfig": "21"}, {"size": 30204, "mtime": 1750403903150, "results": "26", "hashOfConfig": "21"}, {"size": 30846, "mtime": 1751264940370, "results": "27", "hashOfConfig": "21"}, {"size": 27302, "mtime": 1751275257047, "results": "28", "hashOfConfig": "21"}, {"size": 22766, "mtime": 1751276436987, "results": "29", "hashOfConfig": "21"}, {"size": 3713, "mtime": 1751274594344, "results": "30", "hashOfConfig": "21"}, {"size": 21384, "mtime": 1751274594332, "results": "31", "hashOfConfig": "21"}, {"size": 4962, "mtime": 1750146474877, "results": "32", "hashOfConfig": "21"}, {"size": 24393, "mtime": 1751276252940, "results": "33", "hashOfConfig": "21"}, {"size": 39421, "mtime": 1750403903312, "results": "34", "hashOfConfig": "21"}, {"size": 3837, "mtime": 1750403903532, "results": "35", "hashOfConfig": "21"}, {"size": 2848, "mtime": 1750146476432, "results": "36", "hashOfConfig": "21"}, {"size": 4473, "mtime": 1750422157245, "results": "37", "hashOfConfig": "21"}, {"size": 2417, "mtime": 1750654173845, "results": "38", "hashOfConfig": "21"}, {"size": 688, "mtime": 1750423596388, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s4k03p", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js", ["97", "98", "99"], ["100", "101", "102", "103", "104", "105", "106", "107"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js", ["108", "109", "110", "111"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js", ["112"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js", ["113", "114", "115", "116"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js", ["117", "118", "119", "120", "121", "122", "123", "124", "125", "126"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js", ["127", "128", "129", "130"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js", ["131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142"], ["143", "144", "145", "146", "147"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js", ["148", "149"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js", ["150"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js", [], [], {"ruleId": "151", "severity": 1, "message": "152", "line": 31, "column": 40, "nodeType": "153", "messageId": "154", "endLine": 31, "endColumn": 42}, {"ruleId": "151", "severity": 1, "message": "152", "line": 31, "column": 55, "nodeType": "153", "messageId": "154", "endLine": 31, "endColumn": 57}, {"ruleId": "155", "severity": 1, "message": "156", "line": 82, "column": 8, "nodeType": "157", "endLine": 82, "endColumn": 18, "suggestions": "158"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 45, "column": 43, "nodeType": "161", "messageId": "162", "endLine": 45, "endColumn": 45, "suppressions": "163"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 256, "column": 100, "nodeType": "161", "messageId": "162", "endLine": 256, "endColumn": 102, "suppressions": "164"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 256, "column": 126, "nodeType": "161", "messageId": "162", "endLine": 256, "endColumn": 128, "suppressions": "165"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 261, "column": 104, "nodeType": "168", "endLine": 261, "endColumn": 107, "suppressions": "169"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 269, "column": 101, "nodeType": "168", "endLine": 271, "endColumn": 268, "suppressions": "170"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 276, "column": 101, "nodeType": "168", "endLine": 279, "endColumn": 102, "suppressions": "171"}, {"ruleId": "159", "severity": 1, "message": "172", "line": 359, "column": 119, "nodeType": "161", "messageId": "162", "endLine": 359, "endColumn": 121, "suppressions": "173"}, {"ruleId": "159", "severity": 1, "message": "172", "line": 391, "column": 119, "nodeType": "161", "messageId": "162", "endLine": 391, "endColumn": 121, "suppressions": "174"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 16, "column": 5, "nodeType": "177", "messageId": "178", "endLine": 16, "endColumn": 15}, {"ruleId": "175", "severity": 1, "message": "179", "line": 42, "column": 50, "nodeType": "177", "messageId": "178", "endLine": 42, "endColumn": 56}, {"ruleId": "155", "severity": 1, "message": "180", "line": 62, "column": 8, "nodeType": "157", "endLine": 62, "endColumn": 10, "suggestions": "181"}, {"ruleId": "159", "severity": 1, "message": "172", "line": 152, "column": 27, "nodeType": "161", "messageId": "162", "endLine": 152, "endColumn": 29}, {"ruleId": "155", "severity": 1, "message": "180", "line": 81, "column": 8, "nodeType": "157", "endLine": 81, "endColumn": 10, "suggestions": "182"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 26, "column": 5, "nodeType": "177", "messageId": "178", "endLine": 26, "endColumn": 15}, {"ruleId": "175", "severity": 1, "message": "183", "line": 27, "column": 5, "nodeType": "177", "messageId": "178", "endLine": 27, "endColumn": 12}, {"ruleId": "175", "severity": 1, "message": "184", "line": 63, "column": 11, "nodeType": "177", "messageId": "178", "endLine": 63, "endColumn": 25}, {"ruleId": "155", "severity": 1, "message": "180", "line": 76, "column": 8, "nodeType": "157", "endLine": 76, "endColumn": 10, "suggestions": "185"}, {"ruleId": "175", "severity": 1, "message": "186", "line": 26, "column": 22, "nodeType": "177", "messageId": "178", "endLine": 26, "endColumn": 33}, {"ruleId": "175", "severity": 1, "message": "187", "line": 27, "column": 20, "nodeType": "177", "messageId": "178", "endLine": 27, "endColumn": 29}, {"ruleId": "155", "severity": 1, "message": "180", "line": 52, "column": 8, "nodeType": "157", "endLine": 52, "endColumn": 10, "suggestions": "188"}, {"ruleId": "159", "severity": 1, "message": "172", "line": 158, "column": 46, "nodeType": "161", "messageId": "162", "endLine": 158, "endColumn": 48}, {"ruleId": "159", "severity": 1, "message": "160", "line": 182, "column": 44, "nodeType": "161", "messageId": "162", "endLine": 182, "endColumn": 46}, {"ruleId": "159", "severity": 1, "message": "160", "line": 190, "column": 41, "nodeType": "161", "messageId": "162", "endLine": 190, "endColumn": 43}, {"ruleId": "159", "severity": 1, "message": "160", "line": 197, "column": 59, "nodeType": "161", "messageId": "162", "endLine": 197, "endColumn": 61}, {"ruleId": "159", "severity": 1, "message": "172", "line": 201, "column": 30, "nodeType": "161", "messageId": "162", "endLine": 201, "endColumn": 32}, {"ruleId": "159", "severity": 1, "message": "172", "line": 201, "column": 62, "nodeType": "161", "messageId": "162", "endLine": 201, "endColumn": 64}, {"ruleId": "159", "severity": 1, "message": "160", "line": 203, "column": 62, "nodeType": "161", "messageId": "162", "endLine": 203, "endColumn": 64}, {"ruleId": "175", "severity": 1, "message": "176", "line": 20, "column": 5, "nodeType": "177", "messageId": "178", "endLine": 20, "endColumn": 15}, {"ruleId": "175", "severity": 1, "message": "183", "line": 21, "column": 5, "nodeType": "177", "messageId": "178", "endLine": 21, "endColumn": 12}, {"ruleId": "175", "severity": 1, "message": "189", "line": 28, "column": 18, "nodeType": "177", "messageId": "178", "endLine": 28, "endColumn": 31}, {"ruleId": "155", "severity": 1, "message": "180", "line": 73, "column": 8, "nodeType": "157", "endLine": 73, "endColumn": 10, "suggestions": "190"}, {"ruleId": "155", "severity": 1, "message": "156", "line": 37, "column": 8, "nodeType": "157", "endLine": 37, "endColumn": 18, "suggestions": "191"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 82, "column": 36, "nodeType": "161", "messageId": "162", "endLine": 82, "endColumn": 38}, {"ruleId": "159", "severity": 1, "message": "160", "line": 91, "column": 35, "nodeType": "161", "messageId": "162", "endLine": 91, "endColumn": 37}, {"ruleId": "159", "severity": 1, "message": "160", "line": 91, "column": 57, "nodeType": "161", "messageId": "162", "endLine": 91, "endColumn": 59}, {"ruleId": "159", "severity": 1, "message": "160", "line": 160, "column": 19, "nodeType": "161", "messageId": "162", "endLine": 160, "endColumn": 21}, {"ruleId": "159", "severity": 1, "message": "160", "line": 182, "column": 25, "nodeType": "161", "messageId": "162", "endLine": 182, "endColumn": 27}, {"ruleId": "159", "severity": 1, "message": "160", "line": 197, "column": 25, "nodeType": "161", "messageId": "162", "endLine": 197, "endColumn": 27}, {"ruleId": "159", "severity": 1, "message": "172", "line": 199, "column": 47, "nodeType": "161", "messageId": "162", "endLine": 199, "endColumn": 49}, {"ruleId": "159", "severity": 1, "message": "172", "line": 199, "column": 79, "nodeType": "161", "messageId": "162", "endLine": 199, "endColumn": 81}, {"ruleId": "159", "severity": 1, "message": "160", "line": 218, "column": 24, "nodeType": "161", "messageId": "162", "endLine": 218, "endColumn": 26}, {"ruleId": "159", "severity": 1, "message": "160", "line": 251, "column": 22, "nodeType": "161", "messageId": "162", "endLine": 251, "endColumn": 24}, {"ruleId": "159", "severity": 1, "message": "160", "line": 254, "column": 42, "nodeType": "161", "messageId": "162", "endLine": 254, "endColumn": 44}, {"ruleId": "166", "severity": 1, "message": "167", "line": 441, "column": 66, "nodeType": "168", "endLine": 441, "endColumn": 164, "suppressions": "192"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 442, "column": 66, "nodeType": "168", "endLine": 442, "endColumn": 164, "suppressions": "193"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 477, "column": 118, "nodeType": "168", "endLine": 477, "endColumn": 121, "suppressions": "194"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 484, "column": 101, "nodeType": "168", "endLine": 486, "endColumn": 256, "suppressions": "195"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 491, "column": 101, "nodeType": "168", "endLine": 494, "endColumn": 102, "suppressions": "196"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 10, "column": 32, "nodeType": "161", "messageId": "162", "endLine": 10, "endColumn": 34}, {"ruleId": "159", "severity": 1, "message": "160", "line": 11, "column": 35, "nodeType": "161", "messageId": "162", "endLine": 11, "endColumn": 37}, {"ruleId": "175", "severity": 1, "message": "197", "line": 2, "column": 39, "nodeType": "177", "messageId": "178", "endLine": 2, "endColumn": 52}, "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", "ArrayExpression", ["198"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["199"], ["200"], ["201"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["202"], ["203"], ["204"], "Expected '!==' and instead saw '!='.", ["205"], ["206"], "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'errors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", ["207"], ["208"], "'Tooltip' is defined but never used.", "'ProductOptions' is assigned a value but never used.", ["209"], "'setFromDate' is assigned a value but never used.", "'setToDate' is assigned a value but never used.", ["210"], "'DateRangeIcon' is defined but never used.", ["211"], ["212"], ["213"], ["214"], ["215"], ["216"], ["217"], "'clearAuthData' is defined but never used.", {"desc": "218", "fix": "219"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"desc": "222", "fix": "223"}, {"desc": "222", "fix": "224"}, {"desc": "222", "fix": "225"}, {"desc": "222", "fix": "226"}, {"desc": "222", "fix": "227"}, {"desc": "218", "fix": "228"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, {"kind": "220", "justification": "221"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "229", "text": "230"}, "directive", "", "Update the dependencies array to be: [GetAllProcess]", {"range": "231", "text": "232"}, {"range": "233", "text": "232"}, {"range": "234", "text": "232"}, {"range": "235", "text": "232"}, {"range": "236", "text": "232"}, {"range": "237", "text": "230"}, [2987, 2997], "[getTicketDetailsService, ticketId]", [1687, 1689], "[GetAllProcess]", [2488, 2490], [2386, 2388], [1982, 1984], [2129, 2131], [1782, 1792]]