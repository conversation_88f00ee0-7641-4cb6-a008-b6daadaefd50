[{"D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js": "1", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js": "2", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js": "3", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js": "4", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js": "5", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js": "6", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js": "7", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js": "8", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js": "9", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js": "10", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js": "11", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js": "12", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js": "13", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js": "14", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js": "15", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js": "16", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js": "17", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js": "18", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js": "19"}, {"size": 661, "mtime": 1750674074277, "results": "20", "hashOfConfig": "21"}, {"size": 1127, "mtime": 1751266713093, "results": "22", "hashOfConfig": "21"}, {"size": 345, "mtime": 1750146475108, "results": "23", "hashOfConfig": "21"}, {"size": 2588, "mtime": 1750146475047, "results": "24", "hashOfConfig": "21"}, {"size": 6438, "mtime": 1750759167832, "results": "25", "hashOfConfig": "21"}, {"size": 30204, "mtime": 1750403903150, "results": "26", "hashOfConfig": "21"}, {"size": 30846, "mtime": 1751264940370, "results": "27", "hashOfConfig": "21"}, {"size": 19309, "mtime": 1751274594332, "results": "28", "hashOfConfig": "21"}, {"size": 24764, "mtime": 1751274992435, "results": "29", "hashOfConfig": "21"}, {"size": 3713, "mtime": 1751274594344, "results": "30", "hashOfConfig": "21"}, {"size": 21384, "mtime": 1751274594332, "results": "31", "hashOfConfig": "21"}, {"size": 4962, "mtime": 1750146474877, "results": "32", "hashOfConfig": "21"}, {"size": 24433, "mtime": 1751273627460, "results": "33", "hashOfConfig": "21"}, {"size": 39421, "mtime": 1750403903312, "results": "34", "hashOfConfig": "21"}, {"size": 3837, "mtime": 1750403903532, "results": "35", "hashOfConfig": "21"}, {"size": 2848, "mtime": 1750146476432, "results": "36", "hashOfConfig": "21"}, {"size": 4473, "mtime": 1750422157245, "results": "37", "hashOfConfig": "21"}, {"size": 2417, "mtime": 1750654173845, "results": "38", "hashOfConfig": "21"}, {"size": 688, "mtime": 1750423596388, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s4k03p", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js", ["97", "98", "99"], ["100", "101", "102", "103", "104", "105", "106", "107"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js", ["108", "109", "110", "111"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js", ["112", "113", "114"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js", ["115"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js", ["116", "117", "118", "119", "120", "121", "122", "123", "124", "125"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js", ["126", "127", "128", "129"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js", ["130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141"], ["142", "143", "144", "145", "146"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js", ["147", "148"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js", ["149"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js", [], [], {"ruleId": "150", "severity": 1, "message": "151", "line": 31, "column": 40, "nodeType": "152", "messageId": "153", "endLine": 31, "endColumn": 42}, {"ruleId": "150", "severity": 1, "message": "151", "line": 31, "column": 55, "nodeType": "152", "messageId": "153", "endLine": 31, "endColumn": 57}, {"ruleId": "154", "severity": 1, "message": "155", "line": 82, "column": 8, "nodeType": "156", "endLine": 82, "endColumn": 18, "suggestions": "157"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 45, "column": 43, "nodeType": "160", "messageId": "161", "endLine": 45, "endColumn": 45, "suppressions": "162"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 256, "column": 100, "nodeType": "160", "messageId": "161", "endLine": 256, "endColumn": 102, "suppressions": "163"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 256, "column": 126, "nodeType": "160", "messageId": "161", "endLine": 256, "endColumn": 128, "suppressions": "164"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 261, "column": 104, "nodeType": "167", "endLine": 261, "endColumn": 107, "suppressions": "168"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 269, "column": 101, "nodeType": "167", "endLine": 271, "endColumn": 268, "suppressions": "169"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 276, "column": 101, "nodeType": "167", "endLine": 279, "endColumn": 102, "suppressions": "170"}, {"ruleId": "158", "severity": 1, "message": "171", "line": 359, "column": 119, "nodeType": "160", "messageId": "161", "endLine": 359, "endColumn": 121, "suppressions": "172"}, {"ruleId": "158", "severity": 1, "message": "171", "line": 391, "column": 119, "nodeType": "160", "messageId": "161", "endLine": 391, "endColumn": 121, "suppressions": "173"}, {"ruleId": "174", "severity": 1, "message": "175", "line": 16, "column": 5, "nodeType": "176", "messageId": "177", "endLine": 16, "endColumn": 15}, {"ruleId": "174", "severity": 1, "message": "178", "line": 42, "column": 50, "nodeType": "176", "messageId": "177", "endLine": 42, "endColumn": 56}, {"ruleId": "154", "severity": 1, "message": "179", "line": 62, "column": 8, "nodeType": "156", "endLine": 62, "endColumn": 10, "suggestions": "180"}, {"ruleId": "158", "severity": 1, "message": "171", "line": 152, "column": 27, "nodeType": "160", "messageId": "161", "endLine": 152, "endColumn": 29}, {"ruleId": "174", "severity": 1, "message": "181", "line": 26, "column": 22, "nodeType": "176", "messageId": "177", "endLine": 26, "endColumn": 33}, {"ruleId": "174", "severity": 1, "message": "182", "line": 27, "column": 20, "nodeType": "176", "messageId": "177", "endLine": 27, "endColumn": 29}, {"ruleId": "154", "severity": 1, "message": "179", "line": 51, "column": 8, "nodeType": "156", "endLine": 51, "endColumn": 10, "suggestions": "183"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "184", "line": 462, "column": 20, "nodeType": null}, {"ruleId": "174", "severity": 1, "message": "181", "line": 26, "column": 22, "nodeType": "176", "messageId": "177", "endLine": 26, "endColumn": 33}, {"ruleId": "174", "severity": 1, "message": "182", "line": 27, "column": 20, "nodeType": "176", "messageId": "177", "endLine": 27, "endColumn": 29}, {"ruleId": "154", "severity": 1, "message": "179", "line": 52, "column": 8, "nodeType": "156", "endLine": 52, "endColumn": 10, "suggestions": "185"}, {"ruleId": "158", "severity": 1, "message": "171", "line": 158, "column": 46, "nodeType": "160", "messageId": "161", "endLine": 158, "endColumn": 48}, {"ruleId": "158", "severity": 1, "message": "159", "line": 182, "column": 44, "nodeType": "160", "messageId": "161", "endLine": 182, "endColumn": 46}, {"ruleId": "158", "severity": 1, "message": "159", "line": 190, "column": 41, "nodeType": "160", "messageId": "161", "endLine": 190, "endColumn": 43}, {"ruleId": "158", "severity": 1, "message": "159", "line": 197, "column": 59, "nodeType": "160", "messageId": "161", "endLine": 197, "endColumn": 61}, {"ruleId": "158", "severity": 1, "message": "171", "line": 201, "column": 30, "nodeType": "160", "messageId": "161", "endLine": 201, "endColumn": 32}, {"ruleId": "158", "severity": 1, "message": "171", "line": 201, "column": 62, "nodeType": "160", "messageId": "161", "endLine": 201, "endColumn": 64}, {"ruleId": "158", "severity": 1, "message": "159", "line": 203, "column": 62, "nodeType": "160", "messageId": "161", "endLine": 203, "endColumn": 64}, {"ruleId": "174", "severity": 1, "message": "175", "line": 20, "column": 5, "nodeType": "176", "messageId": "177", "endLine": 20, "endColumn": 15}, {"ruleId": "174", "severity": 1, "message": "186", "line": 21, "column": 5, "nodeType": "176", "messageId": "177", "endLine": 21, "endColumn": 12}, {"ruleId": "174", "severity": 1, "message": "187", "line": 28, "column": 18, "nodeType": "176", "messageId": "177", "endLine": 28, "endColumn": 31}, {"ruleId": "154", "severity": 1, "message": "179", "line": 73, "column": 8, "nodeType": "156", "endLine": 73, "endColumn": 10, "suggestions": "188"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 37, "column": 8, "nodeType": "156", "endLine": 37, "endColumn": 18, "suggestions": "189"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 82, "column": 36, "nodeType": "160", "messageId": "161", "endLine": 82, "endColumn": 38}, {"ruleId": "158", "severity": 1, "message": "159", "line": 91, "column": 35, "nodeType": "160", "messageId": "161", "endLine": 91, "endColumn": 37}, {"ruleId": "158", "severity": 1, "message": "159", "line": 91, "column": 57, "nodeType": "160", "messageId": "161", "endLine": 91, "endColumn": 59}, {"ruleId": "158", "severity": 1, "message": "159", "line": 160, "column": 19, "nodeType": "160", "messageId": "161", "endLine": 160, "endColumn": 21}, {"ruleId": "158", "severity": 1, "message": "159", "line": 182, "column": 25, "nodeType": "160", "messageId": "161", "endLine": 182, "endColumn": 27}, {"ruleId": "158", "severity": 1, "message": "159", "line": 197, "column": 25, "nodeType": "160", "messageId": "161", "endLine": 197, "endColumn": 27}, {"ruleId": "158", "severity": 1, "message": "171", "line": 199, "column": 47, "nodeType": "160", "messageId": "161", "endLine": 199, "endColumn": 49}, {"ruleId": "158", "severity": 1, "message": "171", "line": 199, "column": 79, "nodeType": "160", "messageId": "161", "endLine": 199, "endColumn": 81}, {"ruleId": "158", "severity": 1, "message": "159", "line": 218, "column": 24, "nodeType": "160", "messageId": "161", "endLine": 218, "endColumn": 26}, {"ruleId": "158", "severity": 1, "message": "159", "line": 251, "column": 22, "nodeType": "160", "messageId": "161", "endLine": 251, "endColumn": 24}, {"ruleId": "158", "severity": 1, "message": "159", "line": 254, "column": 42, "nodeType": "160", "messageId": "161", "endLine": 254, "endColumn": 44}, {"ruleId": "165", "severity": 1, "message": "166", "line": 441, "column": 66, "nodeType": "167", "endLine": 441, "endColumn": 164, "suppressions": "190"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 442, "column": 66, "nodeType": "167", "endLine": 442, "endColumn": 164, "suppressions": "191"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 477, "column": 118, "nodeType": "167", "endLine": 477, "endColumn": 121, "suppressions": "192"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 484, "column": 101, "nodeType": "167", "endLine": 486, "endColumn": 256, "suppressions": "193"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 491, "column": 101, "nodeType": "167", "endLine": 494, "endColumn": 102, "suppressions": "194"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 10, "column": 32, "nodeType": "160", "messageId": "161", "endLine": 10, "endColumn": 34}, {"ruleId": "158", "severity": 1, "message": "159", "line": 11, "column": 35, "nodeType": "160", "messageId": "161", "endLine": 11, "endColumn": 37}, {"ruleId": "174", "severity": 1, "message": "195", "line": 2, "column": 39, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 52}, "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", "ArrayExpression", ["196"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["197"], ["198"], ["199"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["200"], ["201"], ["202"], "Expected '!==' and instead saw '!='.", ["203"], ["204"], "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'errors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", ["205"], "'setFromDate' is assigned a value but never used.", "'setToDate' is assigned a value but never used.", ["206"], "Parsing error: Expected corresponding JSX closing tag for <div>. (462:20)", ["207"], "'Tooltip' is defined but never used.", "'DateRangeIcon' is defined but never used.", ["208"], ["209"], ["210"], ["211"], ["212"], ["213"], ["214"], "'clearAuthData' is defined but never used.", {"desc": "215", "fix": "216"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"desc": "219", "fix": "220"}, {"desc": "219", "fix": "221"}, {"desc": "219", "fix": "222"}, {"desc": "219", "fix": "223"}, {"desc": "215", "fix": "224"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, {"kind": "217", "justification": "218"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "225", "text": "226"}, "directive", "", "Update the dependencies array to be: [GetAllProcess]", {"range": "227", "text": "228"}, {"range": "229", "text": "228"}, {"range": "230", "text": "228"}, {"range": "231", "text": "228"}, {"range": "232", "text": "226"}, [2987, 2997], "[getTicketDetailsService, ticketId]", [1687, 1689], "[GetAllProcess]", [1917, 1919], [1982, 1984], [2129, 2131], [1782, 1792]]