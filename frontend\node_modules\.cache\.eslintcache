[{"D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js": "1", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js": "2", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js": "3", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js": "4", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js": "5", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js": "6", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js": "7", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js": "8", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js": "9", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js": "10", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js": "11", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js": "12", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js": "13", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js": "14", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js": "15", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js": "16", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js": "17", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js": "18", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js": "19", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js": "20", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js": "21", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js": "22"}, {"size": 661, "mtime": 1750674074277, "results": "23", "hashOfConfig": "24"}, {"size": 1127, "mtime": 1751266713093, "results": "25", "hashOfConfig": "24"}, {"size": 345, "mtime": 1750146475108, "results": "26", "hashOfConfig": "24"}, {"size": 2588, "mtime": 1750146475047, "results": "27", "hashOfConfig": "24"}, {"size": 6438, "mtime": 1750759167832, "results": "28", "hashOfConfig": "24"}, {"size": 30204, "mtime": 1750403903150, "results": "29", "hashOfConfig": "24"}, {"size": 30846, "mtime": 1751264940370, "results": "30", "hashOfConfig": "24"}, {"size": 27296, "mtime": 1751276862495, "results": "31", "hashOfConfig": "24"}, {"size": 9277, "mtime": 1751276847574, "results": "32", "hashOfConfig": "24"}, {"size": 3713, "mtime": 1751274594344, "results": "33", "hashOfConfig": "24"}, {"size": 21384, "mtime": 1751274594332, "results": "34", "hashOfConfig": "24"}, {"size": 4962, "mtime": 1750146474877, "results": "35", "hashOfConfig": "24"}, {"size": 24393, "mtime": 1751276252940, "results": "36", "hashOfConfig": "24"}, {"size": 39421, "mtime": 1750403903312, "results": "37", "hashOfConfig": "24"}, {"size": 3837, "mtime": 1750403903532, "results": "38", "hashOfConfig": "24"}, {"size": 2848, "mtime": 1750146476432, "results": "39", "hashOfConfig": "24"}, {"size": 4473, "mtime": 1750422157245, "results": "40", "hashOfConfig": "24"}, {"size": 2417, "mtime": 1750654173845, "results": "41", "hashOfConfig": "24"}, {"size": 688, "mtime": 1750423596388, "results": "42", "hashOfConfig": "24"}, {"size": 2457, "mtime": 1751276612112, "results": "43", "hashOfConfig": "24"}, {"size": 706, "mtime": 1751276597623, "results": "44", "hashOfConfig": "24"}, {"size": 13084, "mtime": 1751276583193, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s4k03p", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js", ["112", "113", "114"], ["115", "116", "117", "118", "119", "120", "121", "122"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js", ["123", "124", "125", "126"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js", ["127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js", ["138"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js", ["139", "140", "141", "142", "143", "144", "145", "146", "147", "148"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js", ["149", "150", "151", "152"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js", ["153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164"], ["165", "166", "167", "168", "169"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js", ["170", "171"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js", ["172"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js", [], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 31, "column": 40, "nodeType": "175", "messageId": "176", "endLine": 31, "endColumn": 42}, {"ruleId": "173", "severity": 1, "message": "174", "line": 31, "column": 55, "nodeType": "175", "messageId": "176", "endLine": 31, "endColumn": 57}, {"ruleId": "177", "severity": 1, "message": "178", "line": 82, "column": 8, "nodeType": "179", "endLine": 82, "endColumn": 18, "suggestions": "180"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 45, "column": 43, "nodeType": "183", "messageId": "184", "endLine": 45, "endColumn": 45, "suppressions": "185"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 256, "column": 100, "nodeType": "183", "messageId": "184", "endLine": 256, "endColumn": 102, "suppressions": "186"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 256, "column": 126, "nodeType": "183", "messageId": "184", "endLine": 256, "endColumn": 128, "suppressions": "187"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 261, "column": 104, "nodeType": "190", "endLine": 261, "endColumn": 107, "suppressions": "191"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 269, "column": 101, "nodeType": "190", "endLine": 271, "endColumn": 268, "suppressions": "192"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 276, "column": 101, "nodeType": "190", "endLine": 279, "endColumn": 102, "suppressions": "193"}, {"ruleId": "181", "severity": 1, "message": "194", "line": 359, "column": 119, "nodeType": "183", "messageId": "184", "endLine": 359, "endColumn": 121, "suppressions": "195"}, {"ruleId": "181", "severity": 1, "message": "194", "line": 391, "column": 119, "nodeType": "183", "messageId": "184", "endLine": 391, "endColumn": 121, "suppressions": "196"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 16, "column": 5, "nodeType": "199", "messageId": "200", "endLine": 16, "endColumn": 15}, {"ruleId": "197", "severity": 1, "message": "201", "line": 42, "column": 50, "nodeType": "199", "messageId": "200", "endLine": 42, "endColumn": 56}, {"ruleId": "177", "severity": 1, "message": "202", "line": 62, "column": 8, "nodeType": "179", "endLine": 62, "endColumn": 10, "suggestions": "203"}, {"ruleId": "181", "severity": 1, "message": "194", "line": 152, "column": 27, "nodeType": "183", "messageId": "184", "endLine": 152, "endColumn": 29}, {"ruleId": "197", "severity": 1, "message": "204", "line": 28, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 28, "endColumn": 24}, {"ruleId": "197", "severity": 1, "message": "205", "line": 29, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 29, "endColumn": 22}, {"ruleId": "197", "severity": 1, "message": "206", "line": 30, "column": 8, "nodeType": "199", "messageId": "200", "endLine": 30, "endColumn": 21}, {"ruleId": "177", "severity": 1, "message": "202", "line": 80, "column": 8, "nodeType": "179", "endLine": 80, "endColumn": 10, "suggestions": "207"}, {"ruleId": "208", "severity": 2, "message": "209", "line": 278, "column": 57, "nodeType": "210", "messageId": "211", "endLine": 278, "endColumn": 70}, {"ruleId": "208", "severity": 2, "message": "212", "line": 289, "column": 57, "nodeType": "210", "messageId": "211", "endLine": 289, "endColumn": 67}, {"ruleId": "208", "severity": 2, "message": "213", "line": 327, "column": 46, "nodeType": "210", "messageId": "211", "endLine": 327, "endColumn": 60}, {"ruleId": "208", "severity": 2, "message": "214", "line": 337, "column": 54, "nodeType": "210", "messageId": "211", "endLine": 337, "endColumn": 65}, {"ruleId": "208", "severity": 2, "message": "212", "line": 474, "column": 69, "nodeType": "210", "messageId": "211", "endLine": 474, "endColumn": 79}, {"ruleId": "208", "severity": 2, "message": "215", "line": 511, "column": 65, "nodeType": "210", "messageId": "211", "endLine": 511, "endColumn": 75}, {"ruleId": "208", "severity": 2, "message": "216", "line": 521, "column": 42, "nodeType": "210", "messageId": "211", "endLine": 521, "endColumn": 55}, {"ruleId": "177", "severity": 1, "message": "202", "line": 62, "column": 8, "nodeType": "179", "endLine": 62, "endColumn": 10, "suggestions": "217"}, {"ruleId": "197", "severity": 1, "message": "218", "line": 26, "column": 22, "nodeType": "199", "messageId": "200", "endLine": 26, "endColumn": 33}, {"ruleId": "197", "severity": 1, "message": "219", "line": 27, "column": 20, "nodeType": "199", "messageId": "200", "endLine": 27, "endColumn": 29}, {"ruleId": "177", "severity": 1, "message": "202", "line": 52, "column": 8, "nodeType": "179", "endLine": 52, "endColumn": 10, "suggestions": "220"}, {"ruleId": "181", "severity": 1, "message": "194", "line": 158, "column": 46, "nodeType": "183", "messageId": "184", "endLine": 158, "endColumn": 48}, {"ruleId": "181", "severity": 1, "message": "182", "line": 182, "column": 44, "nodeType": "183", "messageId": "184", "endLine": 182, "endColumn": 46}, {"ruleId": "181", "severity": 1, "message": "182", "line": 190, "column": 41, "nodeType": "183", "messageId": "184", "endLine": 190, "endColumn": 43}, {"ruleId": "181", "severity": 1, "message": "182", "line": 197, "column": 59, "nodeType": "183", "messageId": "184", "endLine": 197, "endColumn": 61}, {"ruleId": "181", "severity": 1, "message": "194", "line": 201, "column": 30, "nodeType": "183", "messageId": "184", "endLine": 201, "endColumn": 32}, {"ruleId": "181", "severity": 1, "message": "194", "line": 201, "column": 62, "nodeType": "183", "messageId": "184", "endLine": 201, "endColumn": 64}, {"ruleId": "181", "severity": 1, "message": "182", "line": 203, "column": 62, "nodeType": "183", "messageId": "184", "endLine": 203, "endColumn": 64}, {"ruleId": "197", "severity": 1, "message": "198", "line": 20, "column": 5, "nodeType": "199", "messageId": "200", "endLine": 20, "endColumn": 15}, {"ruleId": "197", "severity": 1, "message": "221", "line": 21, "column": 5, "nodeType": "199", "messageId": "200", "endLine": 21, "endColumn": 12}, {"ruleId": "197", "severity": 1, "message": "222", "line": 28, "column": 18, "nodeType": "199", "messageId": "200", "endLine": 28, "endColumn": 31}, {"ruleId": "177", "severity": 1, "message": "202", "line": 73, "column": 8, "nodeType": "179", "endLine": 73, "endColumn": 10, "suggestions": "223"}, {"ruleId": "177", "severity": 1, "message": "178", "line": 37, "column": 8, "nodeType": "179", "endLine": 37, "endColumn": 18, "suggestions": "224"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 82, "column": 36, "nodeType": "183", "messageId": "184", "endLine": 82, "endColumn": 38}, {"ruleId": "181", "severity": 1, "message": "182", "line": 91, "column": 35, "nodeType": "183", "messageId": "184", "endLine": 91, "endColumn": 37}, {"ruleId": "181", "severity": 1, "message": "182", "line": 91, "column": 57, "nodeType": "183", "messageId": "184", "endLine": 91, "endColumn": 59}, {"ruleId": "181", "severity": 1, "message": "182", "line": 160, "column": 19, "nodeType": "183", "messageId": "184", "endLine": 160, "endColumn": 21}, {"ruleId": "181", "severity": 1, "message": "182", "line": 182, "column": 25, "nodeType": "183", "messageId": "184", "endLine": 182, "endColumn": 27}, {"ruleId": "181", "severity": 1, "message": "182", "line": 197, "column": 25, "nodeType": "183", "messageId": "184", "endLine": 197, "endColumn": 27}, {"ruleId": "181", "severity": 1, "message": "194", "line": 199, "column": 47, "nodeType": "183", "messageId": "184", "endLine": 199, "endColumn": 49}, {"ruleId": "181", "severity": 1, "message": "194", "line": 199, "column": 79, "nodeType": "183", "messageId": "184", "endLine": 199, "endColumn": 81}, {"ruleId": "181", "severity": 1, "message": "182", "line": 218, "column": 24, "nodeType": "183", "messageId": "184", "endLine": 218, "endColumn": 26}, {"ruleId": "181", "severity": 1, "message": "182", "line": 251, "column": 22, "nodeType": "183", "messageId": "184", "endLine": 251, "endColumn": 24}, {"ruleId": "181", "severity": 1, "message": "182", "line": 254, "column": 42, "nodeType": "183", "messageId": "184", "endLine": 254, "endColumn": 44}, {"ruleId": "188", "severity": 1, "message": "189", "line": 441, "column": 66, "nodeType": "190", "endLine": 441, "endColumn": 164, "suppressions": "225"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 442, "column": 66, "nodeType": "190", "endLine": 442, "endColumn": 164, "suppressions": "226"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 477, "column": 118, "nodeType": "190", "endLine": 477, "endColumn": 121, "suppressions": "227"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 484, "column": 101, "nodeType": "190", "endLine": 486, "endColumn": 256, "suppressions": "228"}, {"ruleId": "188", "severity": 1, "message": "189", "line": 491, "column": 101, "nodeType": "190", "endLine": 494, "endColumn": 102, "suppressions": "229"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 10, "column": 32, "nodeType": "183", "messageId": "184", "endLine": 10, "endColumn": 34}, {"ruleId": "181", "severity": 1, "message": "182", "line": 11, "column": 35, "nodeType": "183", "messageId": "184", "endLine": 11, "endColumn": 37}, {"ruleId": "197", "severity": 1, "message": "230", "line": 2, "column": 39, "nodeType": "199", "messageId": "200", "endLine": 2, "endColumn": 52}, "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", "ArrayExpression", ["231"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["232"], ["233"], ["234"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["235"], ["236"], ["237"], "Expected '!==' and instead saw '!='.", ["238"], ["239"], "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'errors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", ["240"], "'TicketPageHeader' is defined but never used.", "'DashboardStats' is defined but never used.", "'DataTableCard' is defined but never used.", ["241"], "react/jsx-no-undef", "'DashboardIcon' is not defined.", "JSXIdentifier", "undefined", "'SearchIcon' is not defined.", "'FilterListIcon' is not defined.", "'RefreshIcon' is not defined.", "'GetAppIcon' is not defined.", "'FeedbackTable' is not defined.", ["242"], "'setFromDate' is assigned a value but never used.", "'setToDate' is assigned a value but never used.", ["243"], "'Tooltip' is defined but never used.", "'DateRangeIcon' is defined but never used.", ["244"], ["245"], ["246"], ["247"], ["248"], ["249"], ["250"], "'clearAuthData' is defined but never used.", {"desc": "251", "fix": "252"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"desc": "255", "fix": "256"}, {"desc": "255", "fix": "257"}, {"desc": "255", "fix": "258"}, {"desc": "255", "fix": "259"}, {"desc": "255", "fix": "260"}, {"desc": "251", "fix": "261"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, {"kind": "253", "justification": "254"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "262", "text": "263"}, "directive", "", "Update the dependencies array to be: [GetAllProcess]", {"range": "264", "text": "265"}, {"range": "266", "text": "265"}, {"range": "267", "text": "265"}, {"range": "268", "text": "265"}, {"range": "269", "text": "265"}, {"range": "270", "text": "263"}, [2987, 2997], "[getTicketDetailsService, ticketId]", [1687, 1689], "[GetAllProcess]", [2482, 2484], [2140, 2142], [1982, 1984], [2129, 2131], [1782, 1792]]