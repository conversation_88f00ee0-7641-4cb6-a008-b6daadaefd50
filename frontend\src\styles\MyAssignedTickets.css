/* MyAssignedTickets Component Styles */

.assigned-tickets-main {
    min-height: 100vh !important;
    background-color: #f8fafc !important;
    padding: 2rem 1rem !important;
    width: 100% !important;
    display: block !important;
}

.assigned-tickets-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 0 !important;
    width: 100% !important;
}

/* Header Section */
.tickets-header {
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 1rem !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    position: relative;
    overflow: hidden;
    box-shadow: none !important;
    border: none !important;
    display: block !important;
    width: 100% !important;
}

.header-decoration-1 {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    z-index: 1 !important;
}

.header-decoration-2 {
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #ffffff1a;
    z-index: 1 !important;
}

.header-content {
    position: relative !important;
    z-index: 10 !important;
    display: flex !important;
    width: 100% !important;
}

.header-icon {
    font-size: 2.5rem !important;
    color: white !important;
}

.header-title {
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    visibility: visible !important;
}

.header-subtitle {
    opacity: 0.9;
    color: white !important;
    display: block !important;
    visibility: visible !important;
}

.header-btn {
    color: white !important;
    border-color: rgba(255,255,255,0.5) !important;
    background-color: transparent !important;
    transition: all 0.3s ease !important;
}

.header-btn:hover {
    background-color: rgba(255,255,255,0.1) !important;
    border-color: white !important;
}

.header-btn.active {
    color: #667eea !important;
    background-color: white !important;
}

.header-btn.active:hover {
    background-color: #f8fafc !important;
}

/* Search Form */
.search-form-card {
    border-radius: 1rem !important;
    background-color: white !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08) !important;
    margin-bottom: 2rem !important;
}

.search-form-content {
    padding: 2rem !important;
}

.search-form-header {
    margin-bottom: 1.5rem !important;
}

.filter-icon {
    color: #667eea !important;
}

.search-form-title {
    color: #1e293b !important;
    font-weight: 600 !important;
}

.form-field .MuiOutlinedInput-root {
    border-radius: 0.5rem !important;
}

.form-field .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
    border-color: #d1d5db !important;
}

.form-field .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
    border-color: #3b82f6 !important;
}

.form-field .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: #3b82f6 !important;
    border-width: 2px !important;
}

.form-field .MuiInputLabel-root {
    color: #475569 !important;
    font-weight: 500 !important;
}

.form-field .MuiInputLabel-root.Mui-focused {
    color: #3b82f6 !important;
}



.search-btn {
    padding: 1rem 2rem !important;
    border-radius: 0.5rem !important;
    background-color: #3b82f6 !important;
    font-weight: 600 !important;
    text-transform: none !important;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
}

.search-btn:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.reset-btn {
    padding: 1rem 2rem !important;
    border-radius: 0.5rem !important;
    border-color: #d1d5db !important;
    color: #64748b !important;
    font-weight: 600 !important;
    text-transform: none !important;
}

.reset-btn:hover {
    border-color: #3b82f6 !important;
    background-color: #f8fafc !important;
    color: #3b82f6 !important;
}

/* Data Table Card */
.data-table-card {
    border-radius: 1rem !important;
    background-color: white !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08) !important;
}

.table-card-content {
    padding: 0 !important;
}

.table-header {
    padding: 1.5rem !important;
    border-bottom: 1px solid #e2e8f0;
}

.table-title {
    color: #1e293b !important;
    font-weight: 600 !important;
}

.table-count-chip {
    background-color: #e0f2fe !important;
    color: #0277bd !important;
    font-weight: 600 !important;
}

.export-btn {
    border-radius: 0.5rem !important;
    border-color: #22c55e !important;
    color: #22c55e !important;
    font-weight: 600 !important;
    text-transform: none !important;
}

.export-btn:hover {
    background-color: #22c55e !important;
    color: white !important;
    transform: translateY(-1px);
}

.table-content {
    padding: 1.5rem !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .assigned-tickets-main {
        padding: 1rem 0.5rem;
    }
    
    .search-form-content {
        padding: 1.5rem !important;
    }
    
   
    
    .search-btn,
    .reset-btn {
        width: 100% !important;
    }
}

@media (max-width: 480px) {
    .header-title {
        font-size: 1.5rem !important;
    }
    
    .header-icon {
        font-size: 2rem !important;
    }
}

/* Animation Classes for MUI */
.MuiGrow-root {
    transform-origin: center !important;
}

.MuiFade-root {
    transition-duration: 0.8s !important;
}

.MuiCard-root, .MuiPaper-root {
    transition: all 0.3s ease !important;
}

/* Override MUI default styles */
.MuiButton-root {
    transition: all 0.3s ease !important;
}

.MuiChip-root {
    transition: all 0.3s ease !important;
}

/* Ensure MUI components display properly */
.MuiGrid2-root {
    display: flex !important;
}

.MuiStack-root {
    display: flex !important;
}

.MuiTypography-root {
    display: block !important;
}

.MuiPaper-root.tickets-header {
    display: block !important;
    visibility: visible !important;
}

.MuiContainer-root {
    display: block !important;
    width: 100% !important;
}

/* Menu Items */
.MuiMenuItem-root {
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
}

.MuiMenuItem-root:hover {
    background-color: #f3f4f6 !important;
}

.MuiMenuItem-root.Mui-selected {
    background-color: #e0f2fe !important;
    color: #0277bd !important;
}

/* Input Placeholder */
.MuiInputBase-input::placeholder {
    color: #9ca3af !important;
    opacity: 1 !important;
} 