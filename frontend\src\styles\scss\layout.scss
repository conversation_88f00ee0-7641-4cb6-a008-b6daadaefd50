/* Main Layout Styles - SCSS Format */

// Variables
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
$border-color: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$success-color: #22c55e;
$info-color: #3b82f6;
$warning-color: #f59e0b;
$error-color: #ef4444;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin transition-smooth {
  transition: all 0.3s ease;
}

@mixin gradient-background($start, $end) {
  background: linear-gradient(135deg, $start 0%, $end 100%);
}

// Main Layout Container
.assigned-tickets-main {
  min-height: 100vh !important;
  background-color: $background-color !important;
  padding: 2rem 1rem !important;
  width: 100% !important;
  display: block !important;
}

.assigned-tickets-container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  width: 100% !important;
}

// Form Field Styling
.form-field {
  .MuiOutlinedInput-root {
    border-radius: 0.5rem !important;
    background-color: $white !important;
    @include transition-smooth;

    &:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
      }
    }

    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
        border-width: 2px !important;
      }
    }
  }

  .MuiInputLabel-root {
    color: $text-secondary !important;
    font-weight: 500 !important;

    &.Mui-focused {
      color: $info-color !important;
    }
  }
}

// Action Buttons Container
// .action-buttons {
//   margin-top: 1rem !important;
// }

// Responsive Design
@media (max-width: 768px) {
  .assigned-tickets-main {
    padding: 1rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .assigned-tickets-main {
    padding: 0.5rem;
  }
}

// MUI Component Overrides
.MuiGrow-root {
  transform-origin: center !important;
}

.MuiFade-root {
  transition-duration: 0.8s !important;
}

.MuiCard-root, 
.MuiPaper-root {
  @include transition-smooth;
}

.MuiButton-root {
  @include transition-smooth;
}

.MuiChip-root {
  @include transition-smooth;
}

.MuiGrid2-root {
  display: flex !important;
}

.MuiStack-root {
  display: flex !important;
}

.MuiTypography-root {
  display: block !important;
}

.MuiContainer-root {
  display: block !important;
  width: 100% !important;
}

.MuiBox-root {
  display: block !important;
}
