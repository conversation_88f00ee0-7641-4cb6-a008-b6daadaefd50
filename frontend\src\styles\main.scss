/* Main SCSS File - All Styles Combined */

// Variables
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
$border-color: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$success-color: #22c55e;
$info-color: #3b82f6;
$warning-color: #f59e0b;
$error-color: #ef4444;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin transition-smooth {
  transition: all 0.3s ease;
}

@mixin gradient-background($start, $end) {
  background: linear-gradient(135deg, $start 0%, $end 100%);
}

// Main Layout Container
.assigned-tickets-main {
  min-height: 100vh !important;
  background-color: $background-color !important;
  padding: 2rem 1rem !important;
  width: 100% !important;
  display: block !important;
}

.assigned-tickets-container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  width: 100% !important;
}

// Header Section
.tickets-header {
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  background-color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #ffffff !important;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
  display: block !important;
  width: 100% !important;
  min-height: 120px !important;
}

.header-decoration-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba($white, 0.1);
  z-index: 1 !important;
}

.header-decoration-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba($white, 0.05);
  z-index: 1 !important;
}

.header-content {
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  width: 100% !important;
}

.header-icon {
  font-size: 2.5rem !important;
  color: $white !important;
}

.header-title {
  font-weight: 700 !important;
  color: #ffffff !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  visibility: visible !important;
}

.header-subtitle {
  opacity: 0.9;
  color: #ffffff !important;
  display: block !important;
  visibility: visible !important;
}

// Ensure header text is visible
.tickets-header .header-title,
.tickets-header .MuiTypography-h4 {
  color: #ffffff !important;
}

.tickets-header .header-subtitle,
.tickets-header .MuiTypography-body1 {
  color: #ffffff !important;
  opacity: 0.9;
}

// Header Buttons
.header-btn {
  color: $white !important;
  border-color: rgba($white, 0.5) !important;
  background-color: transparent !important;
  @include transition-smooth;

  &:hover {
    background-color: rgba($white, 0.1) !important;
    border-color: $white !important;
  }

  &.active {
    color: $primary-color !important;
    background-color: $white !important;

    &:hover {
      background-color: $background-color !important;
    }
  }
}

// Search Form
.search-form-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
  margin-bottom: 2rem !important;
}

.search-form-content {
  padding: 2rem !important;
}

.search-form-header {
  margin-bottom: 1.5rem !important;
}

.filter-icon {
  color: $primary-color !important;
}

.search-form-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

// Form Field Styling
.form-field {
  .MuiOutlinedInput-root {
    border-radius: 0.5rem !important;
    background-color: $white !important;
    @include transition-smooth;

    &:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
      }
    }

    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
        border-width: 2px !important;
      }
    }
  }

  .MuiInputLabel-root {
    color: $text-secondary !important;
    font-weight: 500 !important;

    &.Mui-focused {
      color: $info-color !important;
    }
  }
}

// Search and Reset Buttons
.search-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  background-color: $info-color !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;

  &:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
  }
}

.reset-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  border-color: #d1d5db !important;
  color: $text-secondary !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    border-color: $info-color !important;
    background-color: $background-color !important;
    color: $info-color !important;
  }
}

// Data Table Card
.data-table-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
}

.table-card-content {
  padding: 0 !important;
}

.table-header {
  padding: 1.5rem !important;
  border-bottom: 1px solid $border-color;
}

.table-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

.table-count-chip {
  background-color: #e0f2fe !important;
  color: #0277bd !important;
  font-weight: 600 !important;
}

.export-btn {
  border-radius: 0.5rem !important;
  border-color: $success-color !important;
  color: $success-color !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    background-color: $success-color !important;
    color: $white !important;
    transform: translateY(-1px);
  }
}

.table-content {
  padding: 1.5rem !important;
}

// Dashboard Stats (Feedback Stats)
.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem 0;

  .stat-card {
    background: $white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    @include card-shadow;
    @include transition-smooth;
    cursor: pointer;
    border: 1px solid $border-color;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: $white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: $white;
      opacity: 0.9;
    }

    // Status-specific styling
    &.new-status {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &.open-status {
      background: linear-gradient(135deg, #fcb69f 0%, #ffecd2 100%);
    }

    &.tat-status {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }

    &.resolved-status {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    &.closed-status {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
}

// MyFeedback specific styles
.my-feedback {
  padding: 0;
}

.loading {
  @include flex-center;
  min-height: 200px;
  font-size: 14px;
  color: $text-secondary;
}

// Action Buttons Container
.action-buttons {
  margin-top: 1rem !important;
}

// Responsive Design
@media (max-width: 768px) {
  .assigned-tickets-main {
    padding: 1rem 0.5rem;
  }

  .search-form-content {
    padding: 1.5rem !important;
  }

  .search-btn,
  .reset-btn {
    width: 100% !important;
  }

  .table-header {
    padding: 1rem !important;

    .MuiStack-root {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start !important;
    }
  }

  .table-content {
    padding: 1rem !important;
  }

  .feedback-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;

    .stat-card {
      padding: 1rem;

      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .assigned-tickets-main {
    padding: 0.5rem;
  }

  .header-title {
    font-size: 1.5rem !important;
  }

  .header-icon {
    font-size: 2rem !important;
  }

  .feedback-stats {
    grid-template-columns: 1fr 1fr;

    .stat-card {
      padding: 0.75rem;

      h2 {
        font-size: 1.5rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .table-title {
    font-size: 1.1rem !important;
  }

  .export-btn {
    font-size: 0.8rem !important;
    padding: 0.5rem 1rem !important;
  }
}

// MUI Component Overrides
.MuiGrow-root {
  transform-origin: center !important;
}

.MuiFade-root {
  transition-duration: 0.8s !important;
}

.MuiCard-root,
.MuiPaper-root {
  @include transition-smooth;
}

.MuiButton-root {
  @include transition-smooth;
}

.MuiChip-root {
  @include transition-smooth;
}

.MuiGrid2-root {
  display: flex !important;
}

.MuiStack-root {
  display: flex !important;
}

.MuiTypography-root {
  display: block !important;
}

.MuiContainer-root {
  display: block !important;
  width: 100% !important;
}

.MuiBox-root {
  display: block !important;
}

// Specific overrides for header
.MuiPaper-root.tickets-header,
.tickets-header.MuiPaper-root,
.MuiPaper-elevation0.tickets-header {
  display: block !important;
  visibility: visible !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-color: #667eea !important;
  color: #ffffff !important;
}

// Additional header specificity
.assigned-tickets-main .tickets-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-color: #667eea !important;
}
