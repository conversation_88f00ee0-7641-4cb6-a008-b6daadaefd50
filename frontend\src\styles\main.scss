/* Main SCSS File - All Styles Combined */

// Variables
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
$border-color: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$success-color: #22c55e;
$info-color: #3b82f6;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin transition-smooth {
  transition: all 0.3s ease;
}



// Main Layout Container
.assigned-tickets-main {
  min-height: 100vh !important;
  background-color: $background-color !important;
  padding: 2rem 1rem !important;
  width: 100% !important;
  display: block !important;
}

.assigned-tickets-container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  width: 100% !important;
}

// Header Section
.tickets-header {
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  background-color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #ffffff !important;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
  display: block !important;
  width: 100% !important;
  min-height: 120px !important;
}

.header-decoration-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba($white, 0.1);
  z-index: 1 !important;
}

.header-decoration-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba($white, 0.05);
  z-index: 1 !important;
}

.header-content {
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  width: 100% !important;
}

.header-icon {
  font-size: 2.5rem !important;
  color: $white !important;
}

.header-title {
  font-weight: 700 !important;
  color: #ffffff !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  visibility: visible !important;
}

.header-subtitle {
  opacity: 0.9;
  color: #ffffff !important;
  display: block !important;
  visibility: visible !important;
}

// Ensure header text is visible
.tickets-header .header-title,
.tickets-header .MuiTypography-h4 {
  color: #ffffff !important;
}

.tickets-header .header-subtitle,
.tickets-header .MuiTypography-body1 {
  color: #ffffff !important;
  opacity: 0.9;
}

// Header Buttons
.header-btn {
  color: $white !important;
  border-color: rgba($white, 0.5) !important;
  background-color: transparent !important;
  @include transition-smooth;

  &:hover {
    background-color: rgba($white, 0.1) !important;
    border-color: $white !important;
  }

  &.active {
    color: $primary-color !important;
    background-color: $white !important;

    &:hover {
      background-color: $background-color !important;
    }
  }
}

// Search Form
.search-form-card {
  border-radius: 1rem !important;
  background-color: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
  margin-bottom: 2rem !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.search-form-content {
  padding: 2rem !important;
  display: block !important;
  visibility: visible !important;
}

.search-form-header {
  margin-bottom: 1.5rem !important;
  display: flex !important;
  visibility: visible !important;
}

.filter-icon {
  color: #667eea !important;
}

.search-form-title {
  color: #1e293b !important;
  font-weight: 600 !important;
}

// Form Field Styling
.form-field {
  .MuiOutlinedInput-root {
    border-radius: 0.5rem !important;
    background-color: #ffffff !important;
    transition: all 0.3s ease !important;

    &:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: #3b82f6 !important;
      }
    }

    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: #3b82f6 !important;
        border-width: 2px !important;
      }
    }
  }

  .MuiInputLabel-root {
    color: #64748b !important;
    font-weight: 500 !important;

    &.Mui-focused {
      color: #3b82f6 !important;
    }
  }
}

// Search and Reset Buttons
.search-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  background-color: $info-color !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;

  &:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
  }
}

.reset-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  border-color: #d1d5db !important;
  color: $text-secondary !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    border-color: $info-color !important;
    background-color: $background-color !important;
    color: $info-color !important;
  }
}

// Data Table Card
.data-table-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
}

.table-card-content {
  padding: 0 !important;
}

.table-header {
  padding: 1.5rem !important;
  border-bottom: 1px solid $border-color;
}

.table-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

.table-count-chip {
  background-color: #e0f2fe !important;
  color: #0277bd !important;
  font-weight: 600 !important;
}

.export-btn {
  border-radius: 0.5rem !important;
  border-color: $success-color !important;
  color: $success-color !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    background-color: $success-color !important;
    color: $white !important;
    transform: translateY(-1px);
  }
}

.table-content {
  padding: 1.5rem !important;
}

// Dashboard Stats (Feedback Stats)
.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem 0;

  .stat-card {
    background: $white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    @include card-shadow;
    @include transition-smooth;
    cursor: pointer;
    border: 1px solid $border-color;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: $white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: $white;
      opacity: 0.9;
    }

    // Status-specific styling
    &.new-status {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &.open-status {
      background: linear-gradient(135deg, #fcb69f 0%, #ffecd2 100%);
    }

    &.tat-status {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }

    &.resolved-status {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    &.closed-status {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
}

// MyFeedback specific styles
.my-feedback {
  padding: 0;
}

.loading {
  @include flex-center;
  min-height: 200px;
  font-size: 14px;
  color: $text-secondary;
}

// Responsive Design
@media (max-width: 768px) {
  .assigned-tickets-main {
    padding: 1rem 0.5rem;
  }

  .search-form-content {
    padding: 1.5rem !important;
  }

  .search-btn,
  .reset-btn {
    width: 100% !important;
  }

  .table-header {
    padding: 1rem !important;

    .MuiStack-root {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start !important;
    }
  }

  .table-content {
    padding: 1rem !important;
  }

  .feedback-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;

    .stat-card {
      padding: 1rem;

      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .assigned-tickets-main {
    padding: 0.5rem;
  }

  .header-title {
    font-size: 1.5rem !important;
  }

  .header-icon {
    font-size: 2rem !important;
  }

  .feedback-stats {
    grid-template-columns: 1fr 1fr;

    .stat-card {
      padding: 0.75rem;

      h2 {
        font-size: 1.5rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .table-title {
    font-size: 1.1rem !important;
  }

  .export-btn {
    font-size: 0.8rem !important;
    padding: 0.5rem 1rem !important;
  }
}

// MUI Component Overrides
.MuiGrow-root {
  transform-origin: center !important;
  display: block !important;
  visibility: visible !important;
}

.MuiFade-root {
  transition-duration: 0.8s !important;
}

.MuiCard-root,
.MuiPaper-root {
  @include transition-smooth;
}

.MuiButton-root {
  @include transition-smooth;
}

.MuiChip-root {
  @include transition-smooth;
}

.MuiGrid2-root {
  display: flex !important;
}

.MuiStack-root {
  display: flex !important;
}

.MuiTypography-root {
  display: block !important;
}

.MuiContainer-root {
  display: block !important;
  width: 100% !important;
}

.MuiBox-root {
  display: block !important;
}

// Search Form MUI Overrides
.MuiCard-root.search-form-card {
  display: block !important;
  visibility: visible !important;
}

.MuiCardContent-root.search-form-content {
  display: block !important;
  visibility: visible !important;
}



// Specific overrides for header
.MuiPaper-root.tickets-header,
.tickets-header.MuiPaper-root,
.MuiPaper-elevation0.tickets-header {
  display: block !important;
  visibility: visible !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-color: #667eea !important;
  color: #ffffff !important;
}

// MyTicketDetails Styles
.ticketdetails {
  .detail_links {
    width: 100%;

    > p {
      padding: 0;
      float: right;
      clear: both;
      margin: 0;
      vertical-align: top;
      display: inline-block;
    }
  }

  .assign_hd {
    font-size: 16px;
    color: #40a8fe;
    padding: 0 13px 0 0;
  }

  .btn {
    font-size: 14px;

    &.btn-outline-primary {
      color: #007bff;
      background-color: transparent;
      background-image: none;
      border: 1px solid #007bff;
    }
  }

  .card {
    .header h2 {
      font-size: 16px;
      color: #444;
      position: relative;
    }

    .body {
      color: #444;
      padding: 20px;
      font-weight: 400;
      position: relative;

      &.ticket_detailbox {
        padding-left: 10px;
        padding-right: 10px;
      }

      &.emailer_body {
        padding-left: 10px !important;
        padding-right: 0;
      }
    }

    &.detialbox {
      box-shadow: none !important;
    }
  }

  .table_databox {
    padding: 10px 0 0 0;
  }

  .tab-content > .active {
    display: block;
  }

  .table {
    border: 1px solid #ddd !important;
  }

  .table-responsive {
    position: relative;
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .active_detaillist {
    background: #ddd;
  }

  .detail_data {
    padding-top: 0 !important;
    padding-right: 0 !important;
  }

  // Timeline Styles
  .timeline-item {
    padding: 3em 2em 2em;
    position: relative;
    border-left: 1px solid;

    &.green {
      border-color: #86c541;

      .date {
        color: #86c541;
      }

      &:after {
        border-color: #86c541;
      }
    }

    .date {
      margin-top: -30px;
      display: inline-block;
    }

    &:after {
      width: 10px;
      height: 15px;
      top: 1em;
      position: absolute;
      left: -6px;
      border-radius: 10px;
      content: '';
      background: #fff;
      border: 2px solid;
    }
  }

  .right_section {
    display: inline-block;
    float: right;
  }

  // Mail Compose Section
  .mail_compose_Section {
    width: 100%;
    display: block;
  }

  .shadow_none {
    box-shadow: none;
    margin: 0;
  }

  .compose_box {
    padding-left: 0 !important;
    padding-right: 10px !important;
    width: auto;
    box-sizing: border-box;
    padding-bottom: 0 !important;
  }

  textarea {
    overflow: auto;
    resize: vertical;
  }

  .compose_action_button {
    margin-top: 10px;
    width: 100%;
    display: block;
    float: left;
  }

  // File Upload Styles
  .upload_box {
    display: inline-block;
    padding: 0;
    margin: 3px 0 0 0;
    text-align: left;
    left: 0;
  }

  .inputfile {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;

    + label {
      max-width: 80%;
      font-size: 1.25rem;
      font-weight: 700;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      display: inline-block;
      overflow: hidden;
      padding: 0.625rem 1.25rem;
    }
  }

  .upload_docs {
    background: #007bff !important;
    color: #fff;
    border: none !important;
    border-radius: 4px !important;
    top: -1px !important;
    padding: 6px 4px 6px 9px !important;
  }

  // Mail Inbox Styles
  .mail-inbox {
    display: flex;

    .mail-left {
      width: 280px;
      padding: 15px;
      border-right: 1px solid #eaeaea;
      display: block;

      .mail-side {
        .nav {
          flex-direction: column;

          li {
            padding: 0 10px;
            margin-bottom: 5px;

            label {
              width: 40%;
              font-size: 12px;
              font-weight: normal;
            }

            a {
              color: #666666;
              display: flex;
              align-items: center;

              i {
                font-size: 17px;
                width: 35px;
                transition: font-size 0.2s;
              }

              .badge {
                margin-left: auto;
                margin-right: 0;
              }
            }

            &:hover {
              background-color: #f4f7f6;

              a i {
                font-size: 20px;
              }
            }

            &.active {
              background-color: #f4f7f6;

              a {
                color: #17191c;
              }
            }
          }
        }

        h3 {
          font-size: 15px;
          font-weight: 500;
          margin-bottom: 15px;
          margin-top: 30px;
          line-height: 20px;
        }
      }
    }

    .mail-right {
      width: calc(100% - 280px);
      position: relative;

      &.agent_tkt_view {
        width: 100%;
      }

      .header {
        padding: 15px;

        h2 {
          line-height: 35px;
        }
      }

      .mail-action {
        padding: 15px;

        .fancy-checkbox {
          label {
            margin-bottom: 0;
          }

          input[type="checkbox"] + span:before {
            bottom: 0;
          }
        }

        .pagination-email p {
          line-height: 30px;
          margin-bottom: 0;
        }
      }

      .mail-list {
        padding: 15px 0;

        ul li {
          padding: 17px 15px 15px;
          border-top: 1px solid #eaeaea;
          width: 100%;
          position: relative;

          &:last-child {
            border-bottom: 1px solid #eaeaea;
          }

          .hover-action {
            position: absolute;
            opacity: 0;
            top: 0;
            right: 0;
            padding: 23px 16px 23px;
            background: #ffffff;
            transition: all 0.5s ease-in-out;
          }

          &:hover {
            .hover-action {
              opacity: 1;
            }

            .mail-detail-expand {
              color: #007bff;
            }
          }

          &.unread {
            background-color: #f1f1f1;

            .hover-action {
              background-color: #f1f1f1;
            }
          }

          .mail-detail-left,
          .mail-detail-right {
            float: left;
          }

          .mail-detail-left {
            max-width: 60px;
            min-width: 60px;
            width: 60px;
            position: relative;

            .mail-star {
              position: absolute;
              right: 13px;
              top: 0;
              color: #6c757d;

              &.active {
                color: #ffc107;
              }
            }
          }

          .mail-detail-right {
            position: relative;
            padding-right: 70px;
            width: calc(100% - 70px);

            span.time {
              position: absolute;
              top: 0;
              right: 0;
            }

            h6,
            p {
              width: 100%;
              display: block;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }

            h6 a {
              color: #5a5a5a;
            }

            p {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .mobile-left {
    display: none;
  }

  // Responsive Design for Mail Inbox
  @media screen and (max-width: 991px) {
    .mail-inbox {
      .mail-left {
        width: 230px;
      }

      .mail-right {
        width: calc(100% - 230px);

        .mail-action {
          .btn {
            padding-left: 10px;
            padding-right: 10px;
          }

          .pagination-email p {
            display: none;
          }
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .mobile-left {
      display: block;
      position: fixed;
      z-index: 9999;
      right: 10px;
      bottom: 10px;
    }

    .mail-inbox .mail-left {
      position: absolute;
      left: 0;
      background-color: #ffffff;
      z-index: 99;
    }

    .mail-inbox {
      .mail-left.collapse {
        &:not(.show) {
          display: none;

          + .mail-right {
            width: 100%;
          }
        }
      }

      .mail-right {
        width: 100%;

        .header {
          flex-direction: column;

          .ml-auto {
            margin-left: 0 !important;
            margin-top: 15px;
          }
        }

        .mail-list ul li .mail-detail-right h6 .badge {
          display: none;
        }
      }
    }

    .mail-detail-full a.mail-back {
      top: -60px;
    }

    .media-body p {
      span,
      small {
        display: none;
      }
    }
  }
}