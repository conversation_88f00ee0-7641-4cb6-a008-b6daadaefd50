/* Main SCSS File - All Styles Combined */
.assigned-tickets-main {
  min-height: 100vh !important;
  background-color: #f8fafc !important;
  padding: 2rem 1rem !important;
  width: 100% !important;
  display: block !important;
}

.assigned-tickets-container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  width: 100% !important;
}

.tickets-header {
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff !important;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: none !important;
  display: block !important;
  width: 100% !important;
}

.header-decoration-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1 !important;
}

.header-decoration-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  z-index: 1 !important;
}

.header-content {
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  width: 100% !important;
}

.header-icon {
  font-size: 2.5rem !important;
  color: #ffffff !important;
}

.header-title {
  font-weight: 700 !important;
  color: #ffffff !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  visibility: visible !important;
}

.header-subtitle {
  opacity: 0.9;
  color: #ffffff !important;
  display: block !important;
  visibility: visible !important;
}

.header-btn {
  color: #ffffff !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  background-color: transparent !important;
  transition: all 0.3s ease;
}
.header-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: #ffffff !important;
}
.header-btn.active {
  color: #667eea !important;
  background-color: #ffffff !important;
}
.header-btn.active:hover {
  background-color: #f8fafc !important;
}

.search-form-card {
  border-radius: 1rem !important;
  background-color: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem !important;
}

.search-form-content {
  padding: 2rem !important;
}

.search-form-header {
  margin-bottom: 1.5rem !important;
}

.filter-icon {
  color: #667eea !important;
}

.search-form-title {
  color: #1e293b !important;
  font-weight: 600 !important;
}

.form-field .MuiOutlinedInput-root {
  border-radius: 0.5rem !important;
  background-color: #ffffff !important;
  transition: all 0.3s ease;
}
.form-field .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #3b82f6 !important;
}
.form-field .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #3b82f6 !important;
  border-width: 2px !important;
}
.form-field .MuiInputLabel-root {
  color: #64748b !important;
  font-weight: 500 !important;
}
.form-field .MuiInputLabel-root.Mui-focused {
  color: #3b82f6 !important;
}

.search-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  background-color: #3b82f6 !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
}
.search-btn:hover {
  background-color: #2563eb !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.reset-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  border-color: #d1d5db !important;
  color: #64748b !important;
  font-weight: 600 !important;
  text-transform: none !important;
}
.reset-btn:hover {
  border-color: #3b82f6 !important;
  background-color: #f8fafc !important;
  color: #3b82f6 !important;
}

.data-table-card {
  border-radius: 1rem !important;
  background-color: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.table-card-content {
  padding: 0 !important;
}

.table-header {
  padding: 1.5rem !important;
  border-bottom: 1px solid #e2e8f0;
}

.table-title {
  color: #1e293b !important;
  font-weight: 600 !important;
}

.table-count-chip {
  background-color: #e0f2fe !important;
  color: #0277bd !important;
  font-weight: 600 !important;
}

.export-btn {
  border-radius: 0.5rem !important;
  border-color: #22c55e !important;
  color: #22c55e !important;
  font-weight: 600 !important;
  text-transform: none !important;
}
.export-btn:hover {
  background-color: #22c55e !important;
  color: #ffffff !important;
  transform: translateY(-1px);
}

.table-content {
  padding: 1.5rem !important;
}

.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem 0;
}
.feedback-stats .stat-card {
  background: #ffffff;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}
.feedback-stats .stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}
.feedback-stats .stat-card h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.feedback-stats .stat-card p {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
  opacity: 0.9;
}
.feedback-stats .stat-card.new-status {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.feedback-stats .stat-card.open-status {
  background: linear-gradient(135deg, #fcb69f 0%, #ffecd2 100%);
}
.feedback-stats .stat-card.tat-status {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}
.feedback-stats .stat-card.resolved-status {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.feedback-stats .stat-card.closed-status {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.my-feedback {
  padding: 0;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  font-size: 14px;
  color: #64748b;
}

.action-buttons {
  margin-top: 1rem !important;
}

@media (max-width: 768px) {
  .assigned-tickets-main {
    padding: 1rem 0.5rem;
  }
  .search-form-content {
    padding: 1.5rem !important;
  }
  .search-btn,
  .reset-btn {
    width: 100% !important;
  }
  .table-header {
    padding: 1rem !important;
  }
  .table-header .MuiStack-root {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start !important;
  }
  .table-content {
    padding: 1rem !important;
  }
  .feedback-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  .feedback-stats .stat-card {
    padding: 1rem;
  }
  .feedback-stats .stat-card h2 {
    font-size: 2rem;
  }
  .feedback-stats .stat-card p {
    font-size: 0.9rem;
  }
}
@media (max-width: 480px) {
  .assigned-tickets-main {
    padding: 0.5rem;
  }
  .header-title {
    font-size: 1.5rem !important;
  }
  .header-icon {
    font-size: 2rem !important;
  }
  .feedback-stats {
    grid-template-columns: 1fr 1fr;
  }
  .feedback-stats .stat-card {
    padding: 0.75rem;
  }
  .feedback-stats .stat-card h2 {
    font-size: 1.5rem;
  }
  .feedback-stats .stat-card p {
    font-size: 0.8rem;
  }
  .table-title {
    font-size: 1.1rem !important;
  }
  .export-btn {
    font-size: 0.8rem !important;
    padding: 0.5rem 1rem !important;
  }
}
.MuiGrow-root {
  transform-origin: center !important;
}

.MuiFade-root {
  transition-duration: 0.8s !important;
}

.MuiCard-root,
.MuiPaper-root {
  transition: all 0.3s ease;
}

.MuiButton-root {
  transition: all 0.3s ease;
}

.MuiChip-root {
  transition: all 0.3s ease;
}

.MuiGrid2-root {
  display: flex !important;
}

.MuiStack-root {
  display: flex !important;
}

.MuiTypography-root {
  display: block !important;
}

.MuiContainer-root {
  display: block !important;
  width: 100% !important;
}

.MuiBox-root {
  display: block !important;
}

.MuiPaper-root.tickets-header {
  display: block !important;
  visibility: visible !important;
}

/*# sourceMappingURL=main.css.map */
