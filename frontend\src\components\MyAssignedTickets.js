import React, { useState, useEffect } from 'react';
import {
    Box,
    Container,
    Fade
} from '@mui/material';
import {
    Assignment as AssignmentIcon
} from '@mui/icons-material';
import FeedbackTable from './FeedbackTable';

// Common Components
import TicketPageHeader from './common/TicketPageHeader';
import DashboardStats from './common/DashboardStats';
import DataTableCard from './common/DataTableCard';
import { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';
// import DatePicker from 'react-datepicker';
// import "react-datepicker/dist/react-datepicker.css";
import '../styles/main.scss';
import * as XLSX from 'xlsx';
import alasql from 'alasql';
import { formatDate } from '../services/CommonHelper';

const MyAssignedTickets = () => {
    const [stats, setStats] = useState({
        NEWCASE: 0,
        OPENCASE: 0,
        TATCASE: 0,
        Resolved: 0,
        Closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(2);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    data.unshift({ Name: "Select", SourceID: 0 });
                    setSource(data);
                    if (userDetails?.EMPData[0]?.ProcessID > 0) {
                        const userProcess = data.find(item => item.SourceID === userDetails.EMPData[0].ProcessID);
                        setSelected(prev => ({
                            ...prev,
                            Source: userProcess || { SourceID: userDetails.EMPData[0].ProcessID, Name: 'Unknown Process' }
                        }));
                    }
                }
            })
            .catch(() => {
                setSource([]);
            });
    };

    const GetDashboardCount = (_type) => {
        const objRequest = {
            type: _type,
        };

        GetSalesTicketCount(objRequest)
            .then((data) => {
                if (data.length > 0) {
                    data.forEach(item => {
                        switch (item.StatusID) {
                            case 1:
                                setStats(prev => ({ ...prev, NEWCASE: item.Count }));
                                break;
                            case 2:
                                setStats(prev => ({ ...prev, OPENCASE: item.Count }));
                                break;
                            case 3:
                                setStats(prev => ({ ...prev, Resolved: item.Count }));
                                break;
                            case 4:
                                setStats(prev => ({ ...prev, Closed: item.Count }));
                                break;
                            case 5:
                                setStats(prev => ({ ...prev, TATCASE: item.Count }));
                                break;
                            default:
                                break;
                        }
                    });
                }
            })
            .catch(() => {
                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
            });
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    setIssueSubIssue(data);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
            .then((data) => {
                if (data && data.length > 0) {
                    setStatusList(data);
                }
            })
            .catch(() => {
                setStatusList([]);
            });
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;

        var fromDateStr = formatDateForRequest(fromDate, 3);
        var toDateStr = formatDateForRequest(toDate, 0);

        if (status === 8) {
            fromDateStr = formatDateForRequest(fromDate, 0);
            toDateStr = formatDateForRequest(toDate, 0);
        }

        const obj = {
            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,
            FromDate: fromDateStr,
            ToDate: toDateStr,
            ProcessID: userDetails?.EMPData[0]?.ProcessID ?? 0,
            IssueID: selected.IssueType?.IssueID || 0,
            StatusID: statusId,
            TicketID: 0,
            TicketDisplayID: ticketId?.trim() || "",
            AssignTo: userDetails?.EMPData[0]?.EmpID ?? 0
        };

        GetAdminTicketList(obj)
            .then((data) => {
                if (data && data.length > 0) {
                    const sortedFeedbacks = [...data].sort((a, b) =>
                        new Date(b.CreatedOn) - new Date(a.CreatedOn)
                    );
                    setFeedbacks(sortedFeedbacks);
                } else {
                    setFeedbacks([]);
                }
            })
            .catch(() => {
                setFeedbacks([]);
            });
    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const exportData = () => {

        if (typeof window !== 'undefined') {
            window.XLSX = XLSX;
        }

        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';

            return formatDate(dateStr);
        };

        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,'
            + 'CreatedByDetails -> EmployeeID as EmpID,'
            + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,'
            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'
            + ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ', [feedbacks]
        );
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE || 0, id: 1,  className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE || 0, id: 2, className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5,  className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved || 0, id: 3, className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed || 0, id: 4,  className: 'closed-status' }
    ];

    const resetFilters = () => {
        setSelected({
            Source: { SourceID: 0, Name: 'Select' },
            IssueType: undefined,
            Status: undefined
        });
        setTicketId('');
        setFromDate(new Date());
        setToDate(new Date());
    };

    return (
        <Box className="assigned-tickets-main">
            <Container maxWidth="xl" className="assigned-tickets-container">
                <Fade in timeout={800}>
                    <Box>
                        {/* Header and Search Form */}
                        <TicketPageHeader
                            title="Assigned Tickets"
                            subtitle="Manage and track all assigned feedback tickets"
                            icon={AssignmentIcon}
                            activeSearchType={activeSearchType}
                            setActiveSearchType={setActiveSearchType}
                            resetFilters={resetFilters}
                            fromDate={fromDate}
                            setFromDate={setFromDate}
                            toDate={toDate}
                            setToDate={setToDate}
                            selected={selected}
                            setSelected={setSelected}
                            source={source}
                            issueSubIssue={issueSubIssue}
                            statusList={statusList}
                            ticketId={ticketId}
                            setTicketId={setTicketId}
                            onSearch={() => GetAgentTicketList(8)}
                            showProductField={false}
                            searchButtonText="Search Tickets"
                        />

                        {/* Dashboard Stats */}
                        {activeSearchType === 2 && (
                            <DashboardStats
                                statCards={statCards}
                                onStatClick={GetAgentTicketList}
                            />
                        )}


                        <DataTableCard
                            title="Ticket Results"
                            data={feedbacks}
                            onExport={exportData}
                            showExport={feedbacks.length > 0}
                        >
                            <FeedbackTable feedbacks={feedbacks} type={2} redirectPage='/TicketDetails/' />
                        </DataTableCard>

                       
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default MyAssignedTickets;