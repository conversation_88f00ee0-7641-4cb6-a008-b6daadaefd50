# SCSS Structure Documentation

## Overview
This directory contains the SCSS (Sass) files that have been converted from the original CSS format. The styles are now organized into modular, maintainable SCSS files with variables, mixins, and better organization.

## File Structure

```
scss/
├── main.scss           # Main entry point - imports all modules
├── layout.scss         # Layout, variables, mixins, and base styles
├── header-search.scss  # Header and search form styles
├── data-table.scss     # Data tables and dashboard stats styles
└── README.md          # This documentation file
```

## File Descriptions

### 1. `main.scss`
- **Purpose**: Main entry point that imports all other SCSS modules
- **Contains**: Global styles, utility classes, animations, and print styles
- **Usage**: Import this file in your components: `import '../../styles/scss/main.scss'`

### 2. `layout.scss`
- **Purpose**: Core layout styles, variables, and mixins
- **Contains**:
  - SCSS variables for colors, spacing, etc.
  - Useful mixins for common patterns
  - Main container styles
  - Form field styling
  - MUI component overrides
  - Responsive breakpoints

### 3. `header-search.scss`
- **Purpose**: Header and search form specific styles
- **Contains**:
  - Header section with gradient background
  - Header decorative elements
  - Header buttons (Dashboard/Search toggle)
  - Search form card and content
  - Search and reset buttons
  - Responsive design for header/search

### 4. `data-table.scss`
- **Purpose**: Data tables and dashboard statistics
- **Contains**:
  - Data table card styling
  - Table header and content
  - Export button styling
  - Dashboard stats grid
  - Stat cards with gradients
  - Loading and empty states
  - Table pagination

## SCSS Features Used

### Variables
```scss
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
// ... more variables
```

### Mixins
```scss
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin gradient-background($start, $end) {
  background: linear-gradient(135deg, $start 0%, $end 100%);
}
```

### Nesting
```scss
.search-form-card {
  border-radius: 1rem !important;
  
  .search-form-content {
    padding: 2rem !important;
    
    .search-form-header {
      margin-bottom: 1.5rem !important;
    }
  }
}
```

## Benefits of SCSS Structure

1. **Modularity**: Styles are organized by functionality
2. **Maintainability**: Variables make it easy to change colors/spacing globally
3. **Reusability**: Mixins reduce code duplication
4. **Scalability**: Easy to add new modules or extend existing ones
5. **Better Organization**: Related styles are grouped together

## Usage in Components

### Import in React Components
```javascript
// Import the main SCSS file
import '../../styles/scss/main.scss';
```

### Class Names Remain the Same
All existing CSS class names work exactly the same:
```javascript
<div className="tickets-header">
<div className="search-form-card">
<div className="data-table-card">
```

## Customization

### Changing Colors
Edit variables in `layout.scss`:
```scss
$primary-color: #your-color;
$secondary-color: #your-color;
```

### Adding New Mixins
Add to `layout.scss`:
```scss
@mixin your-mixin($param) {
  // Your styles here
}
```

### Adding New Components
Create new SCSS files and import them in `main.scss`:
```scss
@import './your-new-component.scss';
```

## Migration Notes

- All original CSS functionality is preserved
- Class names remain unchanged
- Components automatically use new SCSS styles
- Better performance due to optimized CSS output
- Easier maintenance and updates

## Development Tips

1. Use variables for consistent spacing and colors
2. Create mixins for repeated patterns
3. Keep related styles in the same file
4. Use nesting judiciously (max 3-4 levels deep)
5. Comment complex calculations or unusual styles

## Browser Support

The compiled CSS supports all modern browsers and IE11+.
SCSS features are compiled to standard CSS, so no browser compatibility issues.
