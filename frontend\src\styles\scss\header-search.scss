/* Header and Search Styles - SCSS Format */

// Variables (duplicated to avoid circular imports)
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
$border-color: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$info-color: #3b82f6;

// Mixins (duplicated to avoid circular imports)
@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin transition-smooth {
  transition: all 0.3s ease;
}

@mixin gradient-background($start, $end) {
  background: linear-gradient(135deg, $start 0%, $end 100%);
}

// Header Section
.tickets-header {
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  @include gradient-background($primary-color, $secondary-color);
  color: $white !important;
  position: relative;
  overflow: hidden;
  @include card-shadow;
  border: none !important;
  display: block !important;
  width: 100% !important;

  // Decorative Elements
  &::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba($white, 0.1);
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba($white, 0.05);
    z-index: 1;
  }
}

.header-decoration-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba($white, 0.1);
  z-index: 1 !important;
}

.header-decoration-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba($white, 0.05);
  z-index: 1 !important;
}

.header-content {
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  width: 100% !important;
}

.header-icon {
  font-size: 2.5rem !important;
  color: $white !important;
}

.header-title {
  font-weight: 700 !important;
  color: $white !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  visibility: visible !important;
}

.header-subtitle {
  opacity: 0.9;
  color: $white !important;
  display: block !important;
  visibility: visible !important;
}

// Header Buttons
.header-btn {
  color: $white !important;
  border-color: rgba($white, 0.5) !important;
  background-color: transparent !important;
  @include transition-smooth;

  &:hover {
    background-color: rgba($white, 0.1) !important;
    border-color: $white !important;
  }

  &.active {
    color: $primary-color !important;
    background-color: $white !important;

    &:hover {
      background-color: $background-color !important;
    }
  }
}

// Search Form
.search-form-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
  margin-bottom: 2rem !important;
}

.search-form-content {
  padding: 2rem !important;
}

.search-form-header {
  margin-bottom: 1.5rem !important;
}

.filter-icon {
  color: $primary-color !important;
}

.search-form-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

// Search and Reset Buttons
.search-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  background-color: $info-color !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;

  &:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
  }
}

.reset-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  border-color: #d1d5db !important;
  color: $text-secondary !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    border-color: $info-color !important;
    background-color: $background-color !important;
    color: $info-color !important;
  }
}

// Responsive Design for Header and Search
@media (max-width: 768px) {
  .search-form-content {
    padding: 1.5rem !important;
  }

  .search-btn,
  .reset-btn {
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 1.5rem !important;
  }

  .header-icon {
    font-size: 2rem !important;
  }
}

// MUI Paper Override for Header
.MuiPaper-root.tickets-header {
  display: block !important;
  visibility: visible !important;
}
