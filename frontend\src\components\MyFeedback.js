import React, { useState, useEffect } from 'react';
import FeedbackTable from './FeedbackTable';
import { GetSalesTicketByAgentId, GetSalesTicketCount } from '../services/feedbackService';
import '../styles/scss/main.scss';

const MyFeedback = () => {
    const [stats, setStats] = useState({
        new: 0,
        open: 0,
        resolved: 0,
        closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {  
        var objRequest = {
            "type": 1,
        };
        
        GetSalesTicketCount(objRequest)
        .then((response) => {
            if(response.length > 0){
                response.forEach(item => {
                    switch (item.StatusID) {
                        case 1:
                            setStats(prev => ({ ...prev, new: item.Count }));
                            break;
                        case 2:
                            setStats(prev => ({ ...prev, open: item.Count }));
                            break;
                        case 3:
                            setStats(prev => ({ ...prev, resolved: item.Count }));
                            break;
                        case 4:
                            setStats(prev => ({ ...prev, closed: item.Count }));
                            break;
                        default:
                            break;
                    }
                });
            }
            else {
                setStats({ new: 0, open: 0, resolved: 0, closed: 0 });
            }
        })
        .catch(() => {
            setStats({ new: 0, open: 0, resolved: 0, closed: 0});
        })
        . finally(() => {
            setLoading(false);
        });
    }, []);


    const GetAllTicketList = (status) => {
        const request = {
            "type": 1,
            "status": status
        };

        GetSalesTicketByAgentId(request)
        .then((response) => {
            if(response.length > 0){
                const sortedFeedbacks = [...response].sort((a, b) => 
                    new Date(b.CreatedOn) - new Date(a.CreatedOn)
                );
                setFeedbacks(sortedFeedbacks);
            }
            else {
                setFeedbacks([]);
            }
        })
        .catch(() => {
            setFeedbacks([]);
        })
    }

    const statCards = [
		{ label: 'New', count: stats.new || 0, Id: 1, color: '#4facfe', className: 'new-status' },
		{ label: 'Open', count: stats.open || 0, Id: 2, color: '#fcb69f', className: 'open-status' },
		{ label: 'Resolved', count: stats.resolved || 0 , Id: 3, color: '#a8edea', className: 'resolved-status' },
		{ label: 'Closed', count: stats.closed || 0, Id: 4, color: '#2c3e50', className: 'closed-status' }
	];

    if (loading) {
        return <div className="loading">Loading...</div>;
    }

    return (
        <div className="my-feedback">
            <p style={{ fontWeight: '600' }}>My FeedBack</p>
            <div className="feedback-stats">
                {statCards.map((stat) => (
                    <div
                        key={stat.label}
                        className={`stat-card ${stat.className}`}
                        style={{ backgroundColor: stat.color }}
                        onClick={() => GetAllTicketList(stat.Id)}
                    >
                        <h2>{stat.count}</h2>
                        <p>{stat.label}</p>
                    </div>
                ))}
            </div>
            <FeedbackTable feedbacks={feedbacks} redirectPage='/MyFeedbackDetails/'/>
        </div>
    );
};

export default MyFeedback; 