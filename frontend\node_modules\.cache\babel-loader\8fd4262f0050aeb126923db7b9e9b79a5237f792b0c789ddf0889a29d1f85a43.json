{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\AllTickets.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Fade } from '@mui/material';\nimport { ViewList as ViewListIcon } from '@mui/icons-material';\n\n// Common Components\nimport TicketPageHeader from './common/TicketPageHeader';\nimport DashboardStats from './common/DashboardStats';\nimport DataTableCard from './common/DataTableCard';\nimport { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';\n// import DatePicker from 'react-datepicker';\n// import \"react-datepicker/dist/react-datepicker.css\";\nimport '../styles/main.scss';\nimport * as XLSX from 'xlsx';\nimport alasql from 'alasql';\nimport { formatDate } from '../services/CommonHelper';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllTickets = () => {\n  _s();\n  const [stats, setStats] = useState({\n    NEWCASE: 0,\n    OPENCASE: 0,\n    TATCASE: 0,\n    Resolved: 0,\n    Closed: 0\n  });\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [source, setSource] = useState([]);\n  const [issueSubIssue, setIssueSubIssue] = useState([]);\n  const [statusList, setStatusList] = useState([]);\n  const [activeSearchType, setActiveSearchType] = useState(2);\n  const [fromDate, setFromDate] = useState(new Date());\n  const [toDate, setToDate] = useState(new Date());\n  const [ticketId, setTicketId] = useState('');\n  const [selected, setSelected] = useState({\n    Source: {\n      SourceID: 0,\n      Name: 'Select'\n    },\n    IssueType: undefined,\n    Status: undefined,\n    Product: {\n      ProductID: 0,\n      Name: 'Select'\n    }\n  });\n  const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));\n  const ProductOptions = [{\n    'ProductID': 0,\n    'Name': 'Select'\n  }, {\n    'ProductID': 115,\n    'Name': 'Investment'\n  }, {\n    'ProductID': 7,\n    'Name': 'Term'\n  }, {\n    'ProductID': 2,\n    'Name': 'Health'\n  }, {\n    'ProductID': 117,\n    'Name': 'Motor'\n  }];\n  useEffect(() => {\n    GetAllProcess();\n    GetDashboardCount(4);\n    getAllStatusMaster();\n    getAllIssueSubIssueService();\n  }, []);\n  const GetAllProcess = () => {\n    GetProcessMasterByAPI().then(data => {\n      if (data && data.length > 0) {\n        var _userDetails$EMPData$;\n        data.unshift({\n          Name: \"Select\",\n          SourceID: 0\n        });\n        setSource(data);\n        if ((userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$EMPData$ = userDetails.EMPData[0]) === null || _userDetails$EMPData$ === void 0 ? void 0 : _userDetails$EMPData$.ProcessID) > 0) {\n          setSelected(prev => ({\n            ...prev,\n            Source: {\n              SourceID: userDetails.EMPData[0].ProcessID\n            }\n          }));\n        }\n      }\n    }).catch(() => {\n      setSource([]);\n    });\n  };\n  const GetDashboardCount = _type => {\n    const objRequest = {\n      type: _type\n    };\n    GetSalesTicketCount(objRequest).then(data => {\n      if (data.length > 0) {\n        data.forEach(item => {\n          switch (item.StatusID) {\n            case 1:\n              setStats(prev => ({\n                ...prev,\n                NEWCASE: item.Count\n              }));\n              break;\n            case 2:\n              setStats(prev => ({\n                ...prev,\n                OPENCASE: item.Count\n              }));\n              break;\n            case 3:\n              setStats(prev => ({\n                ...prev,\n                Resolved: item.Count\n              }));\n              break;\n            case 4:\n              setStats(prev => ({\n                ...prev,\n                Closed: item.Count\n              }));\n              break;\n            case 5:\n              setStats(prev => ({\n                ...prev,\n                TATCASE: item.Count\n              }));\n              break;\n            default:\n              break;\n          }\n        });\n      }\n    }).catch(() => {\n      setStats({\n        NEWCASE: 0,\n        OPENCASE: 0,\n        TATCASE: 0,\n        Resolved: 0,\n        Closed: 0\n      });\n    });\n  };\n  const getAllIssueSubIssueService = () => {\n    GetAllIssueSubIssue().then(data => {\n      if (data && data.length > 0) {\n        setIssueSubIssue(data);\n      }\n    }).catch(() => {\n      setIssueSubIssue([]);\n    });\n  };\n  const getAllStatusMaster = () => {\n    getStatusMaster().then(data => {\n      if (data && data.length > 0) {\n        setStatusList(data);\n      }\n    }).catch(() => {\n      setStatusList([]);\n    });\n  };\n  const GetAgentTicketList = status => {\n    var _selected$Status, _userDetails$EMPData$2, _userDetails$EMPData$3, _selected$Source, _selected$IssueType;\n    const statusId = status !== 8 ? status : ((_selected$Status = selected.Status) === null || _selected$Status === void 0 ? void 0 : _selected$Status.StatusID) || 0;\n    var fromDateStr = formatDateForRequest(fromDate, 3);\n    var toDateStr = formatDateForRequest(toDate, 0);\n    if (status === 8) {\n      fromDateStr = formatDateForRequest(fromDate, 0);\n      toDateStr = formatDateForRequest(toDate, 0);\n    }\n    const obj = {\n      EmpID: (_userDetails$EMPData$2 = userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$EMPData$3 = userDetails.EMPData[0]) === null || _userDetails$EMPData$3 === void 0 ? void 0 : _userDetails$EMPData$3.EmpID) !== null && _userDetails$EMPData$2 !== void 0 ? _userDetails$EMPData$2 : 0,\n      FromDate: fromDateStr,\n      ToDate: toDateStr,\n      ProcessID: ((_selected$Source = selected.Source) === null || _selected$Source === void 0 ? void 0 : _selected$Source.SourceID) || 0,\n      IssueID: ((_selected$IssueType = selected.IssueType) === null || _selected$IssueType === void 0 ? void 0 : _selected$IssueType.IssueID) || 0,\n      StatusID: statusId,\n      TicketID: 0,\n      TicketDisplayID: (ticketId === null || ticketId === void 0 ? void 0 : ticketId.trim()) || \"\",\n      ProductID: selected.Product ? selected.Product.ProductID : 0,\n      FeedBackTypeID: 4\n    };\n    GetAdminTicketList(obj).then(data => {\n      if (data && data.length > 0) {\n        const sortedFeedbacks = [...data].sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn));\n        setFeedbacks(sortedFeedbacks);\n      } else {\n        setFeedbacks([]);\n      }\n    }).catch(() => {\n      setFeedbacks([]);\n    });\n  };\n  const formatDateForRequest = (date, yearDuration = 0) => {\n    const d = new Date(date);\n    const year = d.getFullYear() - yearDuration;\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  };\n  const exportData = () => {\n    if (typeof window !== 'undefined') {\n      window.XLSX = XLSX;\n    }\n    alasql.fn.datetime = function (dateStr) {\n      if (!dateStr) return '';\n      return formatDate(dateStr);\n    };\n    alasql('SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,' + 'CreatedByDetails -> EmployeeID as EmpID,' + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,' + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn' + ' INTO XLSX(\"Data_' + new Date().toDateString() + '.xlsx\", { headers: true }) FROM ? ', [feedbacks]);\n  };\n  const resetFilters = () => {\n    setSelected({\n      Source: {\n        SourceID: 0,\n        Name: 'Select'\n      },\n      IssueType: undefined,\n      Status: undefined,\n      Product: {\n        ProductID: 0,\n        Name: 'Select'\n      }\n    });\n    setTicketId('');\n    setFromDate(new Date());\n    setToDate(new Date());\n  };\n  const statCards = [{\n    label: 'New',\n    count: stats.NEWCASE || 0,\n    id: 1,\n    color: '#4facfe',\n    className: 'new-status'\n  }, {\n    label: 'Open',\n    count: stats.OPENCASE || 0,\n    id: 2,\n    color: '#fcb69f',\n    className: 'open-status'\n  }, {\n    label: 'TAT Bust',\n    count: stats.TATCASE || 0,\n    id: 5,\n    color: '#ff9a9e',\n    className: 'tat-status'\n  }, {\n    label: 'Resolved',\n    count: stats.Resolved || 0,\n    id: 3,\n    color: '#a8edea',\n    className: 'resolved-status'\n  }, {\n    label: 'Closed',\n    count: stats.Closed || 0,\n    id: 4,\n    color: '#667eea',\n    className: 'closed-status'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"assigned-tickets-main\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      className: \"assigned-tickets-container\",\n      children: /*#__PURE__*/_jsxDEV(Fade, {\n        in: true,\n        timeout: 800,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(TicketPageHeader, {\n            title: \"All Tickets\",\n            subtitle: \"View and manage all feedback tickets across the system\",\n            icon: ViewListIcon,\n            activeSearchType: activeSearchType,\n            setActiveSearchType: setActiveSearchType,\n            resetFilters: resetFilters,\n            fromDate: fromDate,\n            setFromDate: setFromDate,\n            toDate: toDate,\n            setToDate: setToDate,\n            selected: selected,\n            setSelected: setSelected,\n            ticketId: ticketId,\n            setTicketId: setTicketId,\n            source: source,\n            issueSubIssue: issueSubIssue,\n            statusList: statusList,\n            ProductOptions: ProductOptions,\n            onSearch: () => GetAgentTicketList(8),\n            showProductField: true,\n            searchButtonText: \"Search Tickets\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 25\n          }, this), activeSearchType === 2 && /*#__PURE__*/_jsxDEV(DashboardStats, {\n            statCards: statCards,\n            onStatClick: GetAgentTicketList\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DataTableCard, {\n            feedbacks: feedbacks,\n            onExport: exportData,\n            tableType: 5,\n            redirectPage: \"/TicketDetails/\",\n            tableTitle: \"All Ticket Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 9\n  }, this);\n};\n_s(AllTickets, \"k8SuDQV1cMbXd0ND3Jqxc5AK+Lo=\");\n_c = AllTickets;\nexport default AllTickets;\nvar _c;\n$RefreshReg$(_c, \"AllTickets\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Fade", "ViewList", "ViewListIcon", "Ticket<PERSON>ageHeader", "DashboardStats", "DataTableCard", "GetSalesTicketCount", "GetProcessMasterByAPI", "GetAllIssueSubIssue", "getStatusMaster", "GetAdminTicketList", "XLSX", "alasql", "formatDate", "jsxDEV", "_jsxDEV", "AllTickets", "_s", "stats", "setStats", "NEWCASE", "OPENCASE", "TATCASE", "Resolved", "Closed", "feedbacks", "setFeedbacks", "source", "setSource", "issueSubIssue", "setIssueSubIssue", "statusList", "setStatusList", "activeSearchType", "setActiveSearchType", "fromDate", "setFromDate", "Date", "toDate", "setToDate", "ticketId", "setTicketId", "selected", "setSelected", "Source", "SourceID", "Name", "IssueType", "undefined", "Status", "Product", "ProductID", "userDetails", "JSON", "parse", "window", "localStorage", "getItem", "ProductOptions", "GetAllProcess", "GetDashboardCount", "getAllStatusMaster", "getAllIssueSubIssueService", "then", "data", "length", "_userDetails$EMPData$", "unshift", "EMPData", "ProcessID", "prev", "catch", "_type", "objRequest", "type", "for<PERSON>ach", "item", "StatusID", "Count", "GetAgentTicketList", "status", "_selected$Status", "_userDetails$EMPData$2", "_userDetails$EMPData$3", "_selected$Source", "_selected$IssueType", "statusId", "fromDateStr", "formatDateForRequest", "toDateStr", "obj", "EmpID", "FromDate", "ToDate", "IssueID", "TicketID", "TicketDisplayID", "trim", "FeedBackTypeID", "sortedFeedbacks", "sort", "a", "b", "CreatedOn", "date", "yearDuration", "d", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "exportData", "fn", "datetime", "dateStr", "toDateString", "resetFilters", "statCards", "label", "count", "id", "color", "className", "children", "max<PERSON><PERSON><PERSON>", "in", "timeout", "title", "subtitle", "icon", "onSearch", "showProductField", "searchButtonText", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStatClick", "onExport", "tableType", "redirectPage", "tableTitle", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/AllTickets.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n    Box,\r\n    Container,\r\n    Fade\r\n} from '@mui/material';\r\nimport {\r\n    ViewList as ViewListIcon\r\n} from '@mui/icons-material';\r\n\r\n// Common Components\r\nimport TicketPageHeader from './common/TicketPageHeader';\r\nimport DashboardStats from './common/DashboardStats';\r\nimport DataTableCard from './common/DataTableCard';\r\nimport { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';\r\n// import DatePicker from 'react-datepicker';\r\n// import \"react-datepicker/dist/react-datepicker.css\";\r\nimport '../styles/main.scss';\r\nimport * as XLSX from 'xlsx';\r\nimport alasql from 'alasql';\r\nimport { formatDate } from '../services/CommonHelper';\r\n\r\nconst AllTickets = () => {\r\n    const [stats, setStats] = useState({\r\n        NEWCASE: 0,\r\n        OPENCASE: 0,\r\n        TATCASE: 0,\r\n        Resolved: 0,\r\n        Closed: 0\r\n    });\r\n\r\n    const [feedbacks, setFeedbacks] = useState([]);\r\n    const [source, setSource] = useState([]);\r\n    const [issueSubIssue, setIssueSubIssue] = useState([]);\r\n    const [statusList, setStatusList] = useState([]);\r\n    const [activeSearchType, setActiveSearchType] = useState(2);\r\n    const [fromDate, setFromDate] = useState(new Date());\r\n    const [toDate, setToDate] = useState(new Date());\r\n    const [ticketId, setTicketId] = useState('');\r\n    const [selected, setSelected] = useState({\r\n        Source: { SourceID: 0, Name: 'Select' },\r\n        IssueType: undefined,\r\n        Status: undefined,\r\n        Product: { ProductID: 0, Name: 'Select' }\r\n    });\r\n\r\n    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));\r\n\r\n    const ProductOptions = [\r\n        { 'ProductID': 0, 'Name': 'Select' },\r\n        { 'ProductID': 115, 'Name': 'Investment' },\r\n        { 'ProductID': 7, 'Name': 'Term' },\r\n        { 'ProductID': 2, 'Name': 'Health' },\r\n        { 'ProductID': 117, 'Name': 'Motor' }\r\n    ];\r\n\r\n    useEffect(() => {\r\n        GetAllProcess();\r\n        GetDashboardCount(4);\r\n        getAllStatusMaster();\r\n        getAllIssueSubIssueService();\r\n    }, []);\r\n\r\n    const GetAllProcess = () => {\r\n        GetProcessMasterByAPI()\r\n            .then((data) => {\r\n                if (data && data.length > 0) {\r\n                    data.unshift({ Name: \"Select\", SourceID: 0 });\r\n                    setSource(data);\r\n                    if (userDetails?.EMPData[0]?.ProcessID > 0) {\r\n                        setSelected(prev => ({\r\n                            ...prev,\r\n                            Source: { SourceID: userDetails.EMPData[0].ProcessID }\r\n                        }));\r\n                    }\r\n                }\r\n            })\r\n            .catch(() => {\r\n                setSource([]);\r\n            });\r\n    };\r\n\r\n    const GetDashboardCount = (_type) => {\r\n        const objRequest = {\r\n            type: _type,\r\n        };\r\n\r\n        GetSalesTicketCount(objRequest)\r\n            .then((data) => {\r\n                if (data.length > 0) {\r\n                    data.forEach(item => {\r\n                        switch (item.StatusID) {\r\n                            case 1:\r\n                                setStats(prev => ({ ...prev, NEWCASE: item.Count }));\r\n                                break;\r\n                            case 2:\r\n                                setStats(prev => ({ ...prev, OPENCASE: item.Count }));\r\n                                break;\r\n                            case 3:\r\n                                setStats(prev => ({ ...prev, Resolved: item.Count }));\r\n                                break;\r\n                            case 4:\r\n                                setStats(prev => ({ ...prev, Closed: item.Count }));\r\n                                break;\r\n                            case 5:\r\n                                setStats(prev => ({ ...prev, TATCASE: item.Count }));\r\n                                break;\r\n                            default:\r\n                                break;\r\n                        }\r\n                    });\r\n                }\r\n            })\r\n            .catch(() => {\r\n                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });\r\n            });\r\n    };\r\n\r\n    const getAllIssueSubIssueService = () => {\r\n        GetAllIssueSubIssue()\r\n            .then((data) => {\r\n                if (data && data.length > 0) {\r\n                    setIssueSubIssue(data);\r\n                }\r\n            })\r\n            .catch(() => {\r\n                setIssueSubIssue([]);\r\n            });\r\n    };\r\n\r\n    const getAllStatusMaster = () => {\r\n        getStatusMaster()\r\n            .then((data) => {\r\n                if (data && data.length > 0) {\r\n                    setStatusList(data);\r\n                }\r\n            })\r\n            .catch(() => {\r\n                setStatusList([]);\r\n            });\r\n    };\r\n\r\n    const GetAgentTicketList = (status) => {\r\n        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;\r\n\r\n        var fromDateStr = formatDateForRequest(fromDate,3);\r\n        var toDateStr = formatDateForRequest(toDate,0);\r\n\r\n        if(status === 8){\r\n            fromDateStr = formatDateForRequest(fromDate,0);\r\n            toDateStr = formatDateForRequest(toDate,0);\r\n        } \r\n\r\n        const obj = {\r\n            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,\r\n            FromDate: fromDateStr,\r\n            ToDate: toDateStr,\r\n            ProcessID: selected.Source?.SourceID || 0,\r\n            IssueID: selected.IssueType?.IssueID || 0,\r\n            StatusID: statusId,\r\n            TicketID: 0,\r\n            TicketDisplayID: ticketId?.trim() || \"\",\r\n            ProductID: selected.Product ? selected.Product.ProductID : 0,\r\n            FeedBackTypeID: 4\r\n        };\r\n\r\n        GetAdminTicketList(obj)\r\n            .then((data) => {\r\n                if (data && data.length > 0) {\r\n                    const sortedFeedbacks = [...data].sort((a, b) => \r\n                        new Date(b.CreatedOn) - new Date(a.CreatedOn)\r\n                    );\r\n                    setFeedbacks(sortedFeedbacks);\r\n                } else {\r\n                    setFeedbacks([]);\r\n                }\r\n            })\r\n            .catch(() => {\r\n                setFeedbacks([]);\r\n            });\r\n    };\r\n\r\n    const formatDateForRequest = (date, yearDuration = 0) => {\r\n        const d = new Date(date);\r\n        const year = d.getFullYear() - yearDuration;\r\n        const month = String(d.getMonth() + 1).padStart(2, '0');\r\n        const day = String(d.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n    };\r\n\r\n    const exportData = () => {\r\n\r\n        if (typeof window !== 'undefined') {\r\n            window.XLSX = XLSX;\r\n        }\r\n\r\n        alasql.fn.datetime = function (dateStr) {\r\n            if (!dateStr) return '';\r\n            \r\n            return formatDate(dateStr);\r\n        };\r\n        \r\n        alasql(\r\n            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,'\r\n            + 'CreatedByDetails -> EmployeeID as EmpID,'\r\n            + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,'\r\n            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'\r\n            + ' INTO XLSX(\"Data_' + new Date().toDateString() + '.xlsx\", { headers: true }) FROM ? ', [feedbacks]\r\n        );\r\n    };\r\n\r\n    const resetFilters = () => {\r\n        setSelected({\r\n            Source: { SourceID: 0, Name: 'Select' },\r\n            IssueType: undefined,\r\n            Status: undefined,\r\n            Product: { ProductID: 0, Name: 'Select' }\r\n        });\r\n        setTicketId('');\r\n        setFromDate(new Date());\r\n        setToDate(new Date());\r\n    };\r\n\r\n    const statCards = [\r\n        { label: 'New', count: stats.NEWCASE || 0, id: 1, color: '#4facfe', className: 'new-status' },\r\n        { label: 'Open', count: stats.OPENCASE || 0, id: 2, color: '#fcb69f', className: 'open-status' },\r\n        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5, color: '#ff9a9e', className: 'tat-status' },\r\n        { label: 'Resolved', count: stats.Resolved || 0, id: 3, color: '#a8edea', className: 'resolved-status' },\r\n        { label: 'Closed', count: stats.Closed || 0, id: 4, color: '#667eea', className: 'closed-status' }\r\n    ];\r\n\r\n    return (\r\n        <Box className=\"assigned-tickets-main\">\r\n            <Container maxWidth=\"xl\" className=\"assigned-tickets-container\">\r\n                <Fade in timeout={800}>\r\n                    <Box>\r\n                        {/* Header and Search Form */}\r\n                        <TicketPageHeader\r\n                            title=\"All Tickets\"\r\n                            subtitle=\"View and manage all feedback tickets across the system\"\r\n                            icon={ViewListIcon}\r\n                            activeSearchType={activeSearchType}\r\n                            setActiveSearchType={setActiveSearchType}\r\n                            resetFilters={resetFilters}\r\n                            fromDate={fromDate}\r\n                            setFromDate={setFromDate}\r\n                            toDate={toDate}\r\n                            setToDate={setToDate}\r\n                            selected={selected}\r\n                            setSelected={setSelected}\r\n                            ticketId={ticketId}\r\n                            setTicketId={setTicketId}\r\n                            source={source}\r\n                            issueSubIssue={issueSubIssue}\r\n                            statusList={statusList}\r\n                            ProductOptions={ProductOptions}\r\n                            onSearch={() => GetAgentTicketList(8)}\r\n                            showProductField={true}\r\n                            searchButtonText=\"Search Tickets\"\r\n                        />\r\n\r\n                        {/* Dashboard Stats */}\r\n                        {activeSearchType === 2 && (\r\n                            <DashboardStats\r\n                                statCards={statCards}\r\n                                onStatClick={GetAgentTicketList}\r\n                            />\r\n                        )}\r\n\r\n                        {/* Data Table */}\r\n                        <DataTableCard\r\n                            feedbacks={feedbacks}\r\n                            onExport={exportData}\r\n                            tableType={5}\r\n                            redirectPage=\"/TicketDetails/\"\r\n                            tableTitle=\"All Ticket Results\"\r\n                        />\r\n                    </Box>\r\n                </Fade>\r\n            </Container>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default AllTickets;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,GAAG,EACHC,SAAS,EACTC,IAAI,QACD,eAAe;AACtB,SACIC,QAAQ,IAAIC,YAAY,QACrB,qBAAqB;;AAE5B;AACA,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,mBAAmB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,6BAA6B;AAClJ;AACA;AACA,OAAO,qBAAqB;AAC5B,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC;IAC/BwB,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,IAAIyC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,IAAIyC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC;IACrCgD,MAAM,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAS,CAAC;IACvCC,SAAS,EAAEC,SAAS;IACpBC,MAAM,EAAED,SAAS;IACjBE,OAAO,EAAE;MAAEC,SAAS,EAAE,CAAC;MAAEL,IAAI,EAAE;IAAS;EAC5C,CAAC,CAAC;EAEF,MAAMM,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;EAE1E,MAAMC,cAAc,GAAG,CACnB;IAAE,WAAW,EAAE,CAAC;IAAE,MAAM,EAAE;EAAS,CAAC,EACpC;IAAE,WAAW,EAAE,GAAG;IAAE,MAAM,EAAE;EAAa,CAAC,EAC1C;IAAE,WAAW,EAAE,CAAC;IAAE,MAAM,EAAE;EAAO,CAAC,EAClC;IAAE,WAAW,EAAE,CAAC;IAAE,MAAM,EAAE;EAAS,CAAC,EACpC;IAAE,WAAW,EAAE,GAAG;IAAE,MAAM,EAAE;EAAQ,CAAC,CACxC;EAED7D,SAAS,CAAC,MAAM;IACZ8D,aAAa,CAAC,CAAC;IACfC,iBAAiB,CAAC,CAAC,CAAC;IACpBC,kBAAkB,CAAC,CAAC;IACpBC,0BAA0B,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMH,aAAa,GAAGA,CAAA,KAAM;IACxBpD,qBAAqB,CAAC,CAAC,CAClBwD,IAAI,CAAEC,IAAI,IAAK;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAAA,IAAAC,qBAAA;QACzBF,IAAI,CAACG,OAAO,CAAC;UAAErB,IAAI,EAAE,QAAQ;UAAED,QAAQ,EAAE;QAAE,CAAC,CAAC;QAC7CjB,SAAS,CAACoC,IAAI,CAAC;QACf,IAAI,CAAAZ,WAAW,aAAXA,WAAW,wBAAAc,qBAAA,GAAXd,WAAW,CAAEgB,OAAO,CAAC,CAAC,CAAC,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBG,SAAS,IAAG,CAAC,EAAE;UACxC1B,WAAW,CAAC2B,IAAI,KAAK;YACjB,GAAGA,IAAI;YACP1B,MAAM,EAAE;cAAEC,QAAQ,EAAEO,WAAW,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACC;YAAU;UACzD,CAAC,CAAC,CAAC;QACP;MACJ;IACJ,CAAC,CAAC,CACDE,KAAK,CAAC,MAAM;MACT3C,SAAS,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC;EACV,CAAC;EAED,MAAMgC,iBAAiB,GAAIY,KAAK,IAAK;IACjC,MAAMC,UAAU,GAAG;MACfC,IAAI,EAAEF;IACV,CAAC;IAEDlE,mBAAmB,CAACmE,UAAU,CAAC,CAC1BV,IAAI,CAAEC,IAAI,IAAK;MACZ,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjBD,IAAI,CAACW,OAAO,CAACC,IAAI,IAAI;UACjB,QAAQA,IAAI,CAACC,QAAQ;YACjB,KAAK,CAAC;cACF1D,QAAQ,CAACmD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAElD,OAAO,EAAEwD,IAAI,CAACE;cAAM,CAAC,CAAC,CAAC;cACpD;YACJ,KAAK,CAAC;cACF3D,QAAQ,CAACmD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjD,QAAQ,EAAEuD,IAAI,CAACE;cAAM,CAAC,CAAC,CAAC;cACrD;YACJ,KAAK,CAAC;cACF3D,QAAQ,CAACmD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE/C,QAAQ,EAAEqD,IAAI,CAACE;cAAM,CAAC,CAAC,CAAC;cACrD;YACJ,KAAK,CAAC;cACF3D,QAAQ,CAACmD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9C,MAAM,EAAEoD,IAAI,CAACE;cAAM,CAAC,CAAC,CAAC;cACnD;YACJ,KAAK,CAAC;cACF3D,QAAQ,CAACmD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEhD,OAAO,EAAEsD,IAAI,CAACE;cAAM,CAAC,CAAC,CAAC;cACpD;YACJ;cACI;UACR;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDP,KAAK,CAAC,MAAM;MACTpD,QAAQ,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC;IAC7E,CAAC,CAAC;EACV,CAAC;EAED,MAAMsC,0BAA0B,GAAGA,CAAA,KAAM;IACrCtD,mBAAmB,CAAC,CAAC,CAChBuD,IAAI,CAAEC,IAAI,IAAK;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACzBnC,gBAAgB,CAACkC,IAAI,CAAC;MAC1B;IACJ,CAAC,CAAC,CACDO,KAAK,CAAC,MAAM;MACTzC,gBAAgB,CAAC,EAAE,CAAC;IACxB,CAAC,CAAC;EACV,CAAC;EAED,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;IAC7BpD,eAAe,CAAC,CAAC,CACZsD,IAAI,CAAEC,IAAI,IAAK;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACzBjC,aAAa,CAACgC,IAAI,CAAC;MACvB;IACJ,CAAC,CAAC,CACDO,KAAK,CAAC,MAAM;MACTvC,aAAa,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACV,CAAC;EAED,MAAM+C,kBAAkB,GAAIC,MAAM,IAAK;IAAA,IAAAC,gBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,mBAAA;IACnC,MAAMC,QAAQ,GAAGN,MAAM,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAAC,gBAAA,GAAAvC,QAAQ,CAACO,MAAM,cAAAgC,gBAAA,uBAAfA,gBAAA,CAAiBJ,QAAQ,KAAI,CAAC;IAEvE,IAAIU,WAAW,GAAGC,oBAAoB,CAACrD,QAAQ,EAAC,CAAC,CAAC;IAClD,IAAIsD,SAAS,GAAGD,oBAAoB,CAAClD,MAAM,EAAC,CAAC,CAAC;IAE9C,IAAG0C,MAAM,KAAK,CAAC,EAAC;MACZO,WAAW,GAAGC,oBAAoB,CAACrD,QAAQ,EAAC,CAAC,CAAC;MAC9CsD,SAAS,GAAGD,oBAAoB,CAAClD,MAAM,EAAC,CAAC,CAAC;IAC9C;IAEA,MAAMoD,GAAG,GAAG;MACRC,KAAK,GAAAT,sBAAA,GAAE9B,WAAW,aAAXA,WAAW,wBAAA+B,sBAAA,GAAX/B,WAAW,CAAEgB,OAAO,CAAC,CAAC,CAAC,cAAAe,sBAAA,uBAAvBA,sBAAA,CAAyBQ,KAAK,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,CAAC;MAC1CU,QAAQ,EAAEL,WAAW;MACrBM,MAAM,EAAEJ,SAAS;MACjBpB,SAAS,EAAE,EAAAe,gBAAA,GAAA1C,QAAQ,CAACE,MAAM,cAAAwC,gBAAA,uBAAfA,gBAAA,CAAiBvC,QAAQ,KAAI,CAAC;MACzCiD,OAAO,EAAE,EAAAT,mBAAA,GAAA3C,QAAQ,CAACK,SAAS,cAAAsC,mBAAA,uBAAlBA,mBAAA,CAAoBS,OAAO,KAAI,CAAC;MACzCjB,QAAQ,EAAES,QAAQ;MAClBS,QAAQ,EAAE,CAAC;MACXC,eAAe,EAAE,CAAAxD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyD,IAAI,CAAC,CAAC,KAAI,EAAE;MACvC9C,SAAS,EAAET,QAAQ,CAACQ,OAAO,GAAGR,QAAQ,CAACQ,OAAO,CAACC,SAAS,GAAG,CAAC;MAC5D+C,cAAc,EAAE;IACpB,CAAC;IAEDxF,kBAAkB,CAACgF,GAAG,CAAC,CAClB3B,IAAI,CAAEC,IAAI,IAAK;MACZ,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACzB,MAAMkC,eAAe,GAAG,CAAC,GAAGnC,IAAI,CAAC,CAACoC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACxC,IAAIjE,IAAI,CAACiE,CAAC,CAACC,SAAS,CAAC,GAAG,IAAIlE,IAAI,CAACgE,CAAC,CAACE,SAAS,CAChD,CAAC;QACD7E,YAAY,CAACyE,eAAe,CAAC;MACjC,CAAC,MAAM;QACHzE,YAAY,CAAC,EAAE,CAAC;MACpB;IACJ,CAAC,CAAC,CACD6C,KAAK,CAAC,MAAM;MACT7C,YAAY,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;EACV,CAAC;EAED,MAAM8D,oBAAoB,GAAGA,CAACgB,IAAI,EAAEC,YAAY,GAAG,CAAC,KAAK;IACrD,MAAMC,CAAC,GAAG,IAAIrE,IAAI,CAACmE,IAAI,CAAC;IACxB,MAAMG,IAAI,GAAGD,CAAC,CAACE,WAAW,CAAC,CAAC,GAAGH,YAAY;IAC3C,MAAMI,KAAK,GAAGC,MAAM,CAACJ,CAAC,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,GAAG,GAAGH,MAAM,CAACJ,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EACpC,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IAErB,IAAI,OAAO5D,MAAM,KAAK,WAAW,EAAE;MAC/BA,MAAM,CAAC5C,IAAI,GAAGA,IAAI;IACtB;IAEAC,MAAM,CAACwG,EAAE,CAACC,QAAQ,GAAG,UAAUC,OAAO,EAAE;MACpC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MAEvB,OAAOzG,UAAU,CAACyG,OAAO,CAAC;IAC9B,CAAC;IAED1G,MAAM,CACF,mHAAmH,GACjH,0CAA0C,GAC1C,qFAAqF,GACrF,gEAAgE,GAChE,mBAAmB,GAAG,IAAIyB,IAAI,CAAC,CAAC,CAACkF,YAAY,CAAC,CAAC,GAAG,oCAAoC,EAAE,CAAC9F,SAAS,CACxG,CAAC;EACL,CAAC;EAED,MAAM+F,YAAY,GAAGA,CAAA,KAAM;IACvB7E,WAAW,CAAC;MACRC,MAAM,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAS,CAAC;MACvCC,SAAS,EAAEC,SAAS;MACpBC,MAAM,EAAED,SAAS;MACjBE,OAAO,EAAE;QAAEC,SAAS,EAAE,CAAC;QAAEL,IAAI,EAAE;MAAS;IAC5C,CAAC,CAAC;IACFL,WAAW,CAAC,EAAE,CAAC;IACfL,WAAW,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMoF,SAAS,GAAG,CACd;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAEzG,KAAK,CAACE,OAAO,IAAI,CAAC;IAAEwG,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC7F;IAAEJ,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAEzG,KAAK,CAACG,QAAQ,IAAI,CAAC;IAAEuG,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAc,CAAC,EAChG;IAAEJ,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAEzG,KAAK,CAACI,OAAO,IAAI,CAAC;IAAEsG,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAa,CAAC,EAClG;IAAEJ,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAEzG,KAAK,CAACK,QAAQ,IAAI,CAAC;IAAEqG,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAkB,CAAC,EACxG;IAAEJ,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAEzG,KAAK,CAACM,MAAM,IAAI,CAAC;IAAEoG,EAAE,EAAE,CAAC;IAAEC,KAAK,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAgB,CAAC,CACrG;EAED,oBACI/G,OAAA,CAACjB,GAAG;IAACgI,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eAClChH,OAAA,CAAChB,SAAS;MAACiI,QAAQ,EAAC,IAAI;MAACF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eAC3DhH,OAAA,CAACf,IAAI;QAACiI,EAAE;QAACC,OAAO,EAAE,GAAI;QAAAH,QAAA,eAClBhH,OAAA,CAACjB,GAAG;UAAAiI,QAAA,gBAEAhH,OAAA,CAACZ,gBAAgB;YACbgI,KAAK,EAAC,aAAa;YACnBC,QAAQ,EAAC,wDAAwD;YACjEC,IAAI,EAAEnI,YAAa;YACnB+B,gBAAgB,EAAEA,gBAAiB;YACnCC,mBAAmB,EAAEA,mBAAoB;YACzCsF,YAAY,EAAEA,YAAa;YAC3BrF,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBE,MAAM,EAAEA,MAAO;YACfC,SAAS,EAAEA,SAAU;YACrBG,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBH,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBd,MAAM,EAAEA,MAAO;YACfE,aAAa,EAAEA,aAAc;YAC7BE,UAAU,EAAEA,UAAW;YACvB2B,cAAc,EAAEA,cAAe;YAC/B4E,QAAQ,EAAEA,CAAA,KAAMvD,kBAAkB,CAAC,CAAC,CAAE;YACtCwD,gBAAgB,EAAE,IAAK;YACvBC,gBAAgB,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,EAGD3G,gBAAgB,KAAK,CAAC,iBACnBlB,OAAA,CAACX,cAAc;YACXqH,SAAS,EAAEA,SAAU;YACrBoB,WAAW,EAAE9D;UAAmB;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACJ,eAGD7H,OAAA,CAACV,aAAa;YACVoB,SAAS,EAAEA,SAAU;YACrBqH,QAAQ,EAAE3B,UAAW;YACrB4B,SAAS,EAAE,CAAE;YACbC,YAAY,EAAC,iBAAiB;YAC9BC,UAAU,EAAC;UAAoB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEd,CAAC;AAAC3H,EAAA,CApQID,UAAU;AAAAkI,EAAA,GAAVlI,UAAU;AAsQhB,eAAeA,UAAU;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}