/* Test CSS for Header Background */

.tickets-header {
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  background-color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #ffffff !important;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
  display: block !important;
  width: 100% !important;
  min-height: 120px !important;
}

.MuiPaper-root.tickets-header,
.tickets-header.MuiPaper-root,
.MuiPaper-elevation0.tickets-header {
  background-color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #ffffff !important;
}

.tickets-header .header-title,
.tickets-header .MuiTypography-h4 {
  color: #ffffff !important;
}

.tickets-header .header-subtitle,
.tickets-header .MuiTypography-body1 {
  color: #ffffff !important;
  opacity: 0.9;
}

.header-icon {
  color: #ffffff !important;
}
