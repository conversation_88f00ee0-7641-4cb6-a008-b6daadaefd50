{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\MyTicketDetails.js\",\n  _s = $RefreshSig$();\n/* eslint-disable eqeqeq */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { formatDate } from '../services/CommonHelper';\nimport { GetDocumentUrl, GetTicketDetails, UpdateTicketRemarks, UploadFile } from '../services/feedbackService';\nimport '../styles/main.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyTicketDetails = () => {\n  _s();\n  var _ticketDetails$Assign, _ticketDetails$Assign2, _selected$Status;\n  const {\n    ticketId\n  } = useParams();\n  const [ticketDetails, setTicketDetails] = useState([]);\n  const [commentList, setCommentList] = useState([]);\n  const [ticketReply, setTicketReply] = useState('');\n  const [fileAttachments, setFileAttachments] = useState([]);\n  const [isSatisfied, setIsSatisfied] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const userDetails = JSON.parse(localStorage.getItem('UserDetails')) || {};\n  const [selected, setSelected] = useState({\n    Status: undefined,\n    IssueType: undefined,\n    SubIssueType: undefined,\n    Source: {\n      SourceID: null\n    }\n  });\n  const statusList = [{\n    StatusID: 3,\n    StatusName: \"Resolved\"\n  }];\n  const isEmpty = str => {\n    return typeof str === 'string' && !str.trim() || typeof str === 'undefined' || str === null;\n  };\n  const getTicketDetailsService = () => {\n    const reqData = {\n      ticketId: ticketId\n    };\n    GetTicketDetails(reqData).then(response => {\n      if (response) {\n        setTicketDetails(response);\n        setCommentList(response.Commentlist || []);\n        if (response.StatusID == 3) {\n          statusList.push({\n            StatusID: 4,\n            StatusName: \"Closed\"\n          });\n        }\n        setSelected(prev => ({\n          ...prev,\n          Status: {\n            StatusID: response.StatusID\n          },\n          IssueType: {\n            ISSUEID: response.IssueID\n          }\n        }));\n      } else {\n        setTicketDetails([]);\n        setCommentList([]);\n        setSelected({\n          Status: undefined,\n          IssueType: undefined,\n          SubIssueType: undefined,\n          Source: {\n            SourceID: null\n          }\n        });\n      }\n    }).catch(() => {\n      setTicketDetails([]);\n      setCommentList([]);\n      setSelected({\n        Status: undefined,\n        IssueType: undefined,\n        SubIssueType: undefined,\n        Source: {\n          SourceID: null\n        }\n      });\n    }).finally(() => {\n      setIsLoading(false);\n    });\n  };\n  useEffect(() => {\n    getTicketDetailsService();\n  }, [ticketId]);\n  const handleFileChange = event => {\n    const files = Array.from(event.target.files);\n    const fileData = [];\n    files.forEach(file => {\n      const reader = new FileReader();\n      reader.onload = e => {\n        const binaryStr = e.target.result;\n        fileData.push({\n          FileName: file.name,\n          AttachemntContent: btoa(binaryStr),\n          AttachmentURL: \"\",\n          ContentType: file.type\n        });\n        if (fileData.length === files.length) {\n          setFileAttachments(fileData);\n        }\n      };\n      reader.readAsBinaryString(file);\n    });\n  };\n  const updateTicketRemarks = replyType => {\n    var _userDetails$EMPData$, _userDetails$EMPData$2;\n    if (!isSatisfied && (isEmpty(ticketReply) || ticketReply.length <= 10)) {\n      toast.error(isEmpty(ticketReply) ? 'Remark should not be blank' : 'Query should be more than 10 char');\n      return;\n    }\n    let ticketStatusId = 0;\n    if (selected.Status.StatusID === 3) {\n      ticketStatusId = 5;\n    }\n    if (isSatisfied === 1) {\n      ticketStatusId = 4;\n    }\n    const requestData = {\n      TicketID: ticketId,\n      Comments: `Comments : ${ticketReply}`,\n      CreatedBy: (_userDetails$EMPData$ = userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$EMPData$2 = userDetails.EMPData[0]) === null || _userDetails$EMPData$2 === void 0 ? void 0 : _userDetails$EMPData$2.EmpID) !== null && _userDetails$EMPData$ !== void 0 ? _userDetails$EMPData$ : 0,\n      StatusID: ticketStatusId,\n      ReplyType: replyType,\n      FileURL: \"\",\n      FileName: \"\",\n      IsSatisfied: isSatisfied,\n      LeadID: ticketDetails.LeadID,\n      ParentID: ticketDetails.ParentID,\n      PayID: ticketDetails.PayID,\n      OrderID: ticketDetails.OrderID,\n      IsStatusChanged: ticketDetails.StatusID !== selected.Status.StatusID\n    };\n    if (fileAttachments.length > 0) {\n      UploadFile(fileAttachments).then(response => {\n        const FileAttachments = response;\n        requestData.FileURL = FileAttachments[0].AttachmentURL;\n        requestData.FileName = FileAttachments[0].FileName;\n        requestData.RefId = FileAttachments[0].RefId;\n        UpdateTicketRemarksService(requestData);\n      }).catch(() => {\n        return;\n      });\n    } else {\n      UpdateTicketRemarksService(requestData);\n    }\n  };\n  const UpdateTicketRemarksService = requestData => {\n    UpdateTicketRemarks(requestData).then(response => {\n      if (response) {\n        toast.success('Updated successfully');\n        setFileAttachments([]);\n        setTicketReply('');\n        getTicketDetailsService();\n      } else {\n        toast.error('Error updating ticket');\n      }\n    }).catch(() => {});\n  };\n  const getDocumentUrl = (docId, refId) => {\n    const requestData = {\n      \"docId\": docId,\n      \"RefId\": refId\n    };\n    GetDocumentUrl(requestData).then(response => {\n      const data = response;\n      if (data !== null && data !== void 0 && data.ttlDocUrl) {\n        window.open(data.ttlDocUrl, '_blank');\n      }\n    }).catch(() => {});\n  };\n  const openInNewTab = url => {\n    if (url) {\n      window.open(url, '_blank');\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid ticketdetails\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"block-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"col-lg-6 col-md-8 col-lg-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"detail_links\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              class: \"demo-button\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                class: \"assign_hd\",\n                children: \"Assigned To :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                class: \"btn btn-outline-primary\",\n                children: [(ticketDetails === null || ticketDetails === void 0 ? void 0 : (_ticketDetails$Assign = ticketDetails.AssignToDetails) === null || _ticketDetails$Assign === void 0 ? void 0 : _ticketDetails$Assign.Name) || 'Not assigned', (ticketDetails === null || ticketDetails === void 0 ? void 0 : (_ticketDetails$Assign2 = ticketDetails.AssignToDetails) === null || _ticketDetails$Assign2 === void 0 ? void 0 : _ticketDetails$Assign2.EmployeeID) && `(${ticketDetails.AssignToDetails.EmployeeID})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"row clearfix\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"col-lg-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"mail-inbox\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"mail-right agent_tkt_view\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"body ticket_detailbox\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"tab-content table_databox\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"tab-pane show active\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"table-responsive\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table m-b-0\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"FeedbackID\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 233,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"CreatedOn\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 234,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Process\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 235,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Feedback\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 236,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Status\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 237,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Last Updated on\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 238,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 232,\n                            columnNumber: 57\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 231,\n                          columnNumber: 53\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                            class: \"active_detaillist\",\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.TicketDisplayID\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 243,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: formatDate(ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.CreatedOn)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 244,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.Process\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 245,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.IssueStatus\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 246,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.TicketStatus\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 247,\n                              columnNumber: 61\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: formatDate(ticketDetails === null || ticketDetails === void 0 ? void 0 : ticketDetails.UpdatedOn)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 248,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 242,\n                            columnNumber: 57\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: commentList && commentList.length > 0 && /*#__PURE__*/_jsxDEV(\"td\", {\n                              colspan: 7,\n                              className: \"tkt_detailbox\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"card detialbox\",\n                                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"body emailer_body\",\n                                  children: [commentList.map((comment, index) => {\n                                    var _comment$User, _comment$User2;\n                                    return (comment.ReplyType == 1 || comment.ReplyType == 2) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: `timeline-item detail_data ${comment.ReplyType === 1 ? 'green' : comment.ReplyType === 2 ? 'blue' : ''}`,\n                                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                        style: {\n                                          margin: '0 0 14px 0'\n                                        },\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"date\",\n                                          children: [\"From : \", /*#__PURE__*/_jsxDEV(\"a\", {\n                                            children: [(_comment$User = comment.User) === null || _comment$User === void 0 ? void 0 : _comment$User.UserName, \"(\", (_comment$User2 = comment.User) === null || _comment$User2 === void 0 ? void 0 : _comment$User2.EmployeeId, \")\"]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 261,\n                                            columnNumber: 104\n                                          }, this)]\n                                        }, void 0, true, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 260,\n                                          columnNumber: 93\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          className: \"right_section\",\n                                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                            className: \"sl-date\",\n                                            children: formatDate(comment.CreatedOn)\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 264,\n                                            columnNumber: 97\n                                          }, this)\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 263,\n                                          columnNumber: 93\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          style: {\n                                            width: '100%',\n                                            display: 'block'\n                                          },\n                                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                                            children: comment.Comment\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 267,\n                                            columnNumber: 97\n                                          }, this), comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') === -1 && /*#__PURE__*/_jsxDEV(\"a\", {\n                                            style: {\n                                              cursor: 'pointer',\n                                              textDecoration: 'underline',\n                                              color: '#007bff'\n                                            },\n                                            onClick: () => getDocumentUrl(comment.FileURL, comment.RefId),\n                                            children: comment.FileName\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 269,\n                                            columnNumber: 101\n                                          }, this), comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') !== -1 && /*#__PURE__*/_jsxDEV(\"a\", {\n                                            style: {\n                                              cursor: 'pointer',\n                                              textDecoration: 'underline',\n                                              color: '#007bff'\n                                            },\n                                            onClick: () => openInNewTab(comment.FileURL),\n                                            children: ticketDetails.RefTicketID\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 276,\n                                            columnNumber: 101\n                                          }, this)]\n                                        }, void 0, true, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 266,\n                                          columnNumber: 93\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 259,\n                                        columnNumber: 89\n                                      }, this)\n                                    }, index, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 258,\n                                      columnNumber: 85\n                                    }, this);\n                                  }), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"mail_compose_Section\",\n                                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"card shadow_none\",\n                                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                        className: \"body compose_box\",\n                                        children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 291,\n                                          columnNumber: 89\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          className: \"header sub_hd\",\n                                          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                                            children: \"Add comment\"\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 293,\n                                            columnNumber: 93\n                                          }, this)\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 292,\n                                          columnNumber: 89\n                                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                                          className: \"form-control mb-3\",\n                                          style: {\n                                            width: '100%'\n                                          },\n                                          rows: \"10\",\n                                          value: ticketReply,\n                                          onChange: e => setTicketReply(e.target.value),\n                                          placeholder: \"Enter your comment...\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 295,\n                                          columnNumber: 89\n                                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                          className: \"m-t-30 compose_action_button\",\n                                          children: [selected.Status.StatusID === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                                            style: {\n                                              fontWeight: 'bold',\n                                              margin: '4px'\n                                            },\n                                            children: [\"This feedback has been marked as resolved, if satisfied please mark 'Yes' to close else 'No' to re-open the same.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 307,\n                                              columnNumber: 131\n                                            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 307,\n                                              columnNumber: 137\n                                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                              className: \"form-check form-check-inline\",\n                                              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                                                type: \"radio\",\n                                                className: \"form-check-input\",\n                                                id: \"satisfiedYes\",\n                                                name: \"satisfied\",\n                                                value: \"1\",\n                                                checked: isSatisfied === 1,\n                                                onChange: e => setIsSatisfied(Number(e.target.value))\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 309,\n                                                columnNumber: 105\n                                              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                                className: \"form-check-label\",\n                                                htmlFor: \"satisfiedYes\",\n                                                children: \"Yes\"\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 318,\n                                                columnNumber: 105\n                                              }, this)]\n                                            }, void 0, true, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 308,\n                                              columnNumber: 101\n                                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                              className: \"form-check form-check-inline\",\n                                              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                                                type: \"radio\",\n                                                className: \"form-check-input\",\n                                                id: \"satisfiedNo\",\n                                                name: \"satisfied\",\n                                                value: \"2\",\n                                                checked: isSatisfied === 2,\n                                                onChange: e => setIsSatisfied(Number(e.target.value))\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 321,\n                                                columnNumber: 105\n                                              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                                className: \"form-check-label\",\n                                                htmlFor: \"satisfiedNo\",\n                                                children: \"No\"\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 330,\n                                                columnNumber: 105\n                                              }, this)]\n                                            }, void 0, true, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 320,\n                                              columnNumber: 101\n                                            }, this)]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 305,\n                                            columnNumber: 97\n                                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                            style: {\n                                              display: 'none',\n                                              width: '100%',\n                                              padding: '0 0 10px 0'\n                                            },\n                                            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                                              children: \"Choose Status\"\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 335,\n                                              columnNumber: 97\n                                            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                                              className: \"form-control\",\n                                              value: ((_selected$Status = selected.Status) === null || _selected$Status === void 0 ? void 0 : _selected$Status.StatusID) || '',\n                                              onChange: e => setSelected(prev => ({\n                                                ...prev,\n                                                Status: {\n                                                  StatusID: parseInt(e.target.value)\n                                                }\n                                              })),\n                                              disabled: false // Replace with your inActive condition\n                                              ,\n                                              style: {\n                                                display: 'inline-block',\n                                                width: 'auto',\n                                                margin: '0 5px 0 0'\n                                              },\n                                              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                                                value: \"\",\n                                                children: \"Select Status\"\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 346,\n                                                columnNumber: 101\n                                              }, this), statusList.filter((status, index, self) => index === self.findIndex(s => s.StatusID === status.StatusID)).sort((a, b) => (a.Sequence || 0) - (b.Sequence || 0)).map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                                                value: status.StatusID,\n                                                children: status.StatusName\n                                              }, status.StatusID, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 352,\n                                                columnNumber: 109\n                                              }, this))]\n                                            }, void 0, true, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 336,\n                                              columnNumber: 97\n                                            }, this)]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 334,\n                                            columnNumber: 93\n                                          }, this), selected.Status.StatusID != 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                                            className: \"upload_box ng-scope\",\n                                            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                                              type: \"file\",\n                                              id: \"file-3\",\n                                              className: \"inputfile inputfile-3 d-none\",\n                                              onChange: handleFileChange,\n                                              multiple: true,\n                                              accept: \".jpg,.pdf,.xlsx\"\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 361,\n                                              columnNumber: 101\n                                            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                                              htmlFor: \"file-3\",\n                                              className: \"upload_docs\",\n                                              style: {\n                                                display: 'inline'\n                                              },\n                                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"20\",\n                                                height: \"17\",\n                                                viewBox: \"0 0 20 17\",\n                                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                                  d: \"M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z\"\n                                                }, void 0, false, {\n                                                  fileName: _jsxFileName,\n                                                  lineNumber: 371,\n                                                  columnNumber: 109\n                                                }, this)\n                                              }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 370,\n                                                columnNumber: 105\n                                              }, this)\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 369,\n                                              columnNumber: 101\n                                            }, this), fileAttachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                                              className: \"mt-2\",\n                                              children: fileAttachments.map((file, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                                                className: \"attachment_files\",\n                                                children: [file.FileName, /*#__PURE__*/_jsxDEV(\"em\", {\n                                                  onClick: () => {\n                                                    const updated = [...fileAttachments];\n                                                    updated.splice(index, 1);\n                                                    setFileAttachments(updated);\n                                                  },\n                                                  children: \"X\"\n                                                }, void 0, false, {\n                                                  fileName: _jsxFileName,\n                                                  lineNumber: 380,\n                                                  columnNumber: 117\n                                                }, this)]\n                                              }, index, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 377,\n                                                columnNumber: 113\n                                              }, this))\n                                            }, void 0, false, {\n                                              fileName: _jsxFileName,\n                                              lineNumber: 375,\n                                              columnNumber: 105\n                                            }, this)]\n                                          }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 360,\n                                            columnNumber: 97\n                                          }, this), selected.Status.StatusID != 4 && /*#__PURE__*/_jsxDEV(\"button\", {\n                                            type: \"button\",\n                                            className: \"btn btn-success\",\n                                            onClick: () => updateTicketRemarks(1),\n                                            children: \"Submit\"\n                                          }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 392,\n                                            columnNumber: 97\n                                          }, this)]\n                                        }, void 0, true, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 303,\n                                          columnNumber: 89\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 290,\n                                        columnNumber: 85\n                                      }, this)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 289,\n                                      columnNumber: 81\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 288,\n                                    columnNumber: 77\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 254,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 253,\n                                columnNumber: 69\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 252,\n                              columnNumber: 65\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 250,\n                            columnNumber: 57\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 241,\n                          columnNumber: 53\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 49\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 9\n  }, this);\n};\n_s(MyTicketDetails, \"OoO1wprghcWZYhIco6PEMQgOljo=\", false, function () {\n  return [useParams];\n});\n_c = MyTicketDetails;\nexport default MyTicketDetails;\nvar _c;\n$RefreshReg$(_c, \"MyTicketDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "toast", "formatDate", "GetDocumentUrl", "GetTicketDetails", "UpdateTicketRemarks", "UploadFile", "jsxDEV", "_jsxDEV", "MyTicketDetails", "_s", "_ticketDetails$Assign", "_ticketDetails$Assign2", "_selected$Status", "ticketId", "ticketDetails", "setTicketDetails", "commentList", "setCommentList", "ticketReply", "setTicketReply", "fileAttachments", "setFileAttachments", "isSatisfied", "setIsSatisfied", "isLoading", "setIsLoading", "userDetails", "JSON", "parse", "localStorage", "getItem", "selected", "setSelected", "Status", "undefined", "IssueType", "SubIssueType", "Source", "SourceID", "statusList", "StatusID", "StatusName", "isEmpty", "str", "trim", "getTicketDetailsService", "reqData", "then", "response", "Commentlist", "push", "prev", "ISSUEID", "IssueID", "catch", "finally", "handleFileChange", "event", "files", "Array", "from", "target", "fileData", "for<PERSON>ach", "file", "reader", "FileReader", "onload", "e", "binaryStr", "result", "FileName", "name", "AttachemntContent", "btoa", "AttachmentURL", "ContentType", "type", "length", "readAsBinaryString", "updateTicketRemarks", "replyType", "_userDetails$EMPData$", "_userDetails$EMPData$2", "error", "ticketStatusId", "requestData", "TicketID", "Comments", "CreatedBy", "EMPData", "EmpID", "ReplyType", "FileURL", "IsSatisfied", "LeadID", "ParentID", "PayID", "OrderID", "IsStatusChanged", "FileAttachments", "RefId", "UpdateTicketRemarksService", "success", "getDocumentUrl", "docId", "refId", "data", "ttlDocUrl", "window", "open", "openInNewTab", "url", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "class", "AssignToDetails", "Name", "EmployeeID", "TicketDisplayID", "CreatedOn", "Process", "IssueStatus", "TicketStatus", "UpdatedOn", "colspan", "map", "comment", "index", "_comment$User", "_comment$User2", "style", "margin", "User", "UserName", "EmployeeId", "width", "display", "Comment", "indexOf", "cursor", "textDecoration", "color", "onClick", "RefTicketID", "rows", "value", "onChange", "placeholder", "fontWeight", "id", "checked", "Number", "htmlFor", "padding", "parseInt", "disabled", "filter", "status", "self", "findIndex", "s", "sort", "a", "b", "Sequence", "multiple", "accept", "xmlns", "height", "viewBox", "d", "updated", "splice", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/MyTicketDetails.js"], "sourcesContent": ["/* eslint-disable eqeqeq */\r\n/* eslint-disable jsx-a11y/anchor-is-valid */\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { toast } from 'react-toastify';\r\nimport { formatDate } from '../services/CommonHelper';\r\nimport { GetDocumentUrl, GetTicketDetails, UpdateTicketRemarks, UploadFile } from '../services/feedbackService';\r\nimport '../styles/main.scss';\r\n\r\nconst MyTicketDetails = () => {\r\n    const { ticketId } = useParams();\r\n    const [ticketDetails, setTicketDetails] = useState([]);\r\n    const [commentList, setCommentList] = useState([]);\r\n    const [ticketReply, setTicketReply] = useState('');\r\n    const [fileAttachments, setFileAttachments] = useState([]);\r\n    const [isSatisfied, setIsSatisfied] = useState(0);\r\n    const [isLoading, setIsLoading] = useState(true);\r\n    const userDetails = JSON.parse(localStorage.getItem('UserDetails')) || {};\r\n    const [selected, setSelected] = useState({\r\n        Status: undefined,\r\n        IssueType: undefined,\r\n        SubIssueType: undefined,\r\n        Source: { SourceID: null }\r\n    });\r\n\r\n    const statusList = [\r\n        { StatusID: 3, StatusName: \"Resolved\" }\r\n    ];\r\n\r\n    const isEmpty = (str) => {\r\n        return typeof str === 'string' && !str.trim() || typeof str === 'undefined' || str === null;\r\n    };\r\n\r\n    const getTicketDetailsService = () => {\r\n        const reqData = {\r\n            ticketId: ticketId\r\n        }\r\n\r\n        GetTicketDetails(reqData)\r\n            .then((response) => {\r\n                if (response) {\r\n                    setTicketDetails(response);\r\n                    setCommentList(response.Commentlist || []);\r\n\r\n                    if (response.StatusID == 3) {\r\n                        statusList.push({ StatusID: 4, StatusName: \"Closed\" });\r\n                    }\r\n\r\n                    setSelected(prev => ({\r\n                        ...prev,\r\n                        Status: { StatusID: response.StatusID },\r\n                        IssueType: { ISSUEID: response.IssueID }\r\n                    }));\r\n                } else {\r\n                    setTicketDetails([]);\r\n                    setCommentList([]);\r\n                    setSelected({\r\n                        Status: undefined,\r\n                        IssueType: undefined,\r\n                        SubIssueType: undefined,\r\n                        Source: { SourceID: null }\r\n                    });\r\n                }\r\n            })\r\n            .catch(() => {\r\n                setTicketDetails([]);\r\n                setCommentList([]);\r\n                setSelected({\r\n                    Status: undefined,\r\n                    IssueType: undefined,\r\n                    SubIssueType: undefined,\r\n                    Source: { SourceID: null }\r\n                });\r\n            })\r\n            .finally(() => {\r\n                setIsLoading(false);\r\n            });\r\n    };\r\n\r\n    useEffect(() => {\r\n        getTicketDetailsService();\r\n    }, [ticketId]);\r\n\r\n\r\n    const handleFileChange = (event) => {\r\n        const files = Array.from(event.target.files);\r\n        const fileData = [];\r\n\r\n        files.forEach(file => {\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                const binaryStr = e.target.result;\r\n                fileData.push({\r\n                    FileName: file.name,\r\n                    AttachemntContent: btoa(binaryStr),\r\n                    AttachmentURL: \"\",\r\n                    ContentType: file.type\r\n                });\r\n\r\n                if (fileData.length === files.length) {\r\n                    setFileAttachments(fileData);\r\n                }\r\n            };\r\n            reader.readAsBinaryString(file);\r\n        });\r\n    };\r\n\r\n    const updateTicketRemarks = (replyType) => {\r\n        if (!isSatisfied && (isEmpty(ticketReply) || ticketReply.length <= 10)) {\r\n            toast.error(isEmpty(ticketReply) ? 'Remark should not be blank' : 'Query should be more than 10 char');\r\n            return;\r\n        }\r\n\r\n        let ticketStatusId = 0;\r\n        if (selected.Status.StatusID === 3) {\r\n            ticketStatusId = 5;\r\n        }\r\n        if (isSatisfied === 1) {\r\n            ticketStatusId = 4;\r\n        }\r\n\r\n        const requestData = {\r\n            TicketID: ticketId,\r\n            Comments: `Comments : ${ticketReply}`,\r\n            CreatedBy: userDetails?.EMPData[0]?.EmpID ?? 0,\r\n            StatusID: ticketStatusId,\r\n            ReplyType: replyType,\r\n            FileURL: \"\",\r\n            FileName: \"\",\r\n            IsSatisfied: isSatisfied,\r\n            LeadID: ticketDetails.LeadID,\r\n            ParentID: ticketDetails.ParentID,\r\n            PayID: ticketDetails.PayID,\r\n            OrderID: ticketDetails.OrderID,\r\n            IsStatusChanged: ticketDetails.StatusID !== selected.Status.StatusID\r\n        };\r\n\r\n        if (fileAttachments.length > 0) {\r\n            \r\n            UploadFile(fileAttachments)\r\n            .then((response) => {\r\n                const FileAttachments = response;\r\n                requestData.FileURL = FileAttachments[0].AttachmentURL;\r\n                requestData.FileName = FileAttachments[0].FileName;\r\n                requestData.RefId = FileAttachments[0].RefId;\r\n\r\n                UpdateTicketRemarksService(requestData);\r\n            })\r\n            .catch(() => {\r\n                return;\r\n            })\r\n        } else {\r\n            UpdateTicketRemarksService(requestData);\r\n        }\r\n    };\r\n\r\n    const UpdateTicketRemarksService = (requestData) => {\r\n        UpdateTicketRemarks(requestData)\r\n        .then ((response) => {\r\n            if(response) {\r\n                toast.success('Updated successfully');\r\n                setFileAttachments([]);\r\n                setTicketReply('');\r\n                getTicketDetailsService();\r\n            }\r\n            else {\r\n                toast.error('Error updating ticket');\r\n            }\r\n        })\r\n        .catch(() => {\r\n\r\n        })\r\n    }\r\n \r\n    const getDocumentUrl = (docId, refId) => {\r\n        const requestData = {\r\n            \"docId\": docId,\r\n            \"RefId\": refId\r\n        }\r\n\r\n        GetDocumentUrl(requestData)\r\n        .then((response) => {\r\n            const data = response;\r\n            if (data?.ttlDocUrl) {\r\n                window.open(data.ttlDocUrl, '_blank');\r\n            }\r\n        })\r\n        .catch(() => {\r\n\r\n        })   \r\n    };\r\n\r\n    const openInNewTab = (url) => {\r\n        if (url) {\r\n            window.open(url, '_blank');\r\n        }\r\n    };\r\n\r\n    if (isLoading) {\r\n        return <div>Loading...</div>;\r\n    }\r\n\r\n    return (\r\n        <div className=\"container-fluid ticketdetails\">\r\n            <div class=\"block-header\">\r\n                <div class=\"row\">\r\n                    <div class=\"col-lg-6 col-md-8 col-lg-12\">\r\n                        <div class=\"detail_links\">\r\n                            <p class=\"demo-button\">\r\n                                <span class=\"assign_hd\">Assigned To :</span>\r\n                                <button type=\"button\" class=\"btn btn-outline-primary\">{ticketDetails?.AssignToDetails?.Name || 'Not assigned'}\r\n                                    {ticketDetails?.AssignToDetails?.EmployeeID &&\r\n                                        `(${ticketDetails.AssignToDetails.EmployeeID})`}\r\n                                </button>\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"row clearfix\">\r\n                <div class=\"col-lg-12\">\r\n                    <div class=\"card\">\r\n                        <div class=\"mail-inbox\">\r\n                            <div class=\"mail-right agent_tkt_view\">\r\n                                <div class=\"body ticket_detailbox\">\r\n                                    <div class=\"tab-content table_databox\">\r\n                                        <div class=\"tab-pane show active\">\r\n                                            <div className=\"table-responsive\">\r\n                                                <table className=\"table m-b-0\">\r\n                                                    <thead>\r\n                                                        <tr>\r\n                                                            <th>FeedbackID</th>\r\n                                                            <th>CreatedOn</th>\r\n                                                            <th>Process</th>\r\n                                                            <th>Feedback</th>\r\n                                                            <th>Status</th>\r\n                                                            <th>Last Updated on</th>\r\n                                                        </tr>\r\n                                                    </thead>\r\n                                                    <tbody>\r\n                                                        <tr class=\"active_detaillist\">\r\n                                                            <td>{ticketDetails?.TicketDisplayID}</td>\r\n                                                            <td>{formatDate(ticketDetails?.CreatedOn)}</td>\r\n                                                            <td>{ticketDetails?.Process}</td>\r\n                                                            <td>{ticketDetails?.IssueStatus}</td>\r\n                                                            <td>{ticketDetails?.TicketStatus}</td>\r\n                                                            <td>{formatDate(ticketDetails?.UpdatedOn)}</td>\r\n                                                        </tr>\r\n                                                        <tr>\r\n                                                            {commentList && commentList.length > 0 && (\r\n                                                                <td colspan={7} className=\"tkt_detailbox\">\r\n                                                                    <div className=\"card detialbox\">\r\n                                                                        <div className=\"body emailer_body\">\r\n                                                                            {commentList.map((comment, index) => (\r\n                                                                                (comment.ReplyType == 1 || comment.ReplyType == 2)\r\n                                                                                && (\r\n                                                                                    <div key={index} className={`timeline-item detail_data ${comment.ReplyType === 1 ? 'green' : comment.ReplyType === 2 ? 'blue' : ''}`}>\r\n                                                                                        <div style={{ margin: '0 0 14px 0' }}>\r\n                                                                                            <span className=\"date\">\r\n                                                                                                From : <a>{comment.User?.UserName}({comment.User?.EmployeeId})</a>\r\n                                                                                            </span>\r\n                                                                                            <div className=\"right_section\">\r\n                                                                                                <span className=\"sl-date\">{formatDate(comment.CreatedOn)}</span>\r\n                                                                                            </div>\r\n                                                                                            <div style={{ width: '100%', display: 'block' }}>\r\n                                                                                                <p>{comment.Comment}</p>\r\n                                                                                                {comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') === -1 && (\r\n                                                                                                    <a\r\n                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}\r\n                                                                                                        onClick={() => getDocumentUrl(comment.FileURL, comment.RefId)}                                                                                                    >\r\n                                                                                                        {comment.FileName}\r\n                                                                                                    </a>)\r\n                                                                                                }\r\n                                                                                                {comment.FileURL && comment.FileURL !== -1 && comment.FileURL.indexOf('https') !== -1 && (\r\n                                                                                                    <a\r\n                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}\r\n                                                                                                        onClick={() => openInNewTab(comment.FileURL)}\r\n                                                                                                    >\r\n                                                                                                        {ticketDetails.RefTicketID}\r\n                                                                                                    </a>)\r\n                                                                                                }\r\n                                                                                            </div>\r\n                                                                                        </div>\r\n                                                                                    </div>\r\n                                                                                )\r\n                                                                            ))}\r\n                                                                            <div className=\"mail_compose_Section\">\r\n                                                                                <div className=\"card shadow_none\">\r\n                                                                                    <div className=\"body compose_box\">\r\n                                                                                        <hr />\r\n                                                                                        <div className=\"header sub_hd\">\r\n                                                                                            <h2>Add comment</h2>\r\n                                                                                        </div>  \r\n                                                                                        <textarea\r\n                                                                                            className=\"form-control mb-3\"\r\n                                                                                            style={{ width: '100%' }}\r\n                                                                                            rows=\"10\"\r\n                                                                                            value={ticketReply}\r\n                                                                                            onChange={(e) => setTicketReply(e.target.value)}\r\n                                                                                            placeholder=\"Enter your comment...\"\r\n                                                                                        />\r\n                                                                                        <div className=\"m-t-30 compose_action_button\">\r\n                                                                                            {selected.Status.StatusID === 3 && (\r\n                                                                                                <div style={{ fontWeight: 'bold', margin: '4px' }}>\r\n                                                                                                    This feedback has been marked as resolved, if satisfied please mark 'Yes' to close\r\n                                                                                                    else 'No' to re-open the same.<br /><br />\r\n                                                                                                    <div className=\"form-check form-check-inline\">\r\n                                                                                                        <input\r\n                                                                                                            type=\"radio\"\r\n                                                                                                            className=\"form-check-input\"\r\n                                                                                                            id=\"satisfiedYes\"\r\n                                                                                                            name=\"satisfied\"\r\n                                                                                                            value=\"1\"\r\n                                                                                                            checked={isSatisfied === 1}\r\n                                                                                                            onChange={(e) => setIsSatisfied(Number(e.target.value))}\r\n                                                                                                        />\r\n                                                                                                        <label className=\"form-check-label\" htmlFor=\"satisfiedYes\">Yes</label>\r\n                                                                                                    </div>\r\n                                                                                                    <div className=\"form-check form-check-inline\">\r\n                                                                                                        <input\r\n                                                                                                            type=\"radio\"\r\n                                                                                                            className=\"form-check-input\"\r\n                                                                                                            id=\"satisfiedNo\"\r\n                                                                                                            name=\"satisfied\"\r\n                                                                                                            value=\"2\"\r\n                                                                                                            checked={isSatisfied === 2}\r\n                                                                                                            onChange={(e) => setIsSatisfied(Number(e.target.value))}\r\n                                                                                                        />\r\n                                                                                                        <label className=\"form-check-label\" htmlFor=\"satisfiedNo\">No</label>\r\n                                                                                                    </div>\r\n                                                                                                </div>\r\n                                                                                            )}\r\n                                                                                            <div style={{ display: 'none', width: '100%', padding: '0 0 10px 0' }}>\r\n                                                                                                <label>Choose Status</label>\r\n                                                                                                <select \r\n                                                                                                    className=\"form-control\" \r\n                                                                                                    value={selected.Status?.StatusID || ''} \r\n                                                                                                    onChange={(e) => setSelected(prev => ({\r\n                                                                                                        ...prev,\r\n                                                                                                        Status: { StatusID: parseInt(e.target.value) }\r\n                                                                                                    }))}\r\n                                                                                                    disabled={false} // Replace with your inActive condition\r\n                                                                                                    style={{ display: 'inline-block', width: 'auto', margin: '0 5px 0 0' }}\r\n                                                                                                >\r\n                                                                                                    <option value=\"\">Select Status</option>\r\n                                                                                                    {statusList\r\n                                                                                                        .filter((status, index, self) => \r\n                                                                                                            index === self.findIndex(s => s.StatusID === status.StatusID))\r\n                                                                                                        .sort((a, b) => (a.Sequence || 0) - (b.Sequence || 0))\r\n                                                                                                        .map(status => (\r\n                                                                                                            <option key={status.StatusID} value={status.StatusID}>\r\n                                                                                                                {status.StatusName}\r\n                                                                                                            </option>\r\n                                                                                                        ))\r\n                                                                                                    }\r\n                                                                                                </select>\r\n                                                                                            </div>\r\n                                                                                            {selected.Status.StatusID != 4 && (\r\n                                                                                                <div className=\"upload_box ng-scope\">\r\n                                                                                                    <input\r\n                                                                                                        type=\"file\"\r\n                                                                                                        id=\"file-3\"\r\n                                                                                                        className=\"inputfile inputfile-3 d-none\"\r\n                                                                                                        onChange={handleFileChange}\r\n                                                                                                        multiple\r\n                                                                                                        accept=\".jpg,.pdf,.xlsx\"\r\n                                                                                                    />\r\n                                                                                                    <label htmlFor=\"file-3\" className=\"upload_docs\" style={{ display: 'inline' }}>\r\n                                                                                                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"17\" viewBox=\"0 0 20 17\">\r\n                                                                                                            <path d=\"M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z\" />\r\n                                                                                                        </svg>\r\n                                                                                                    </label>\r\n                                                                                                    {fileAttachments.length > 0 && (\r\n                                                                                                        <div className=\"mt-2\">\r\n                                                                                                            {fileAttachments.map((file, index) => (\r\n                                                                                                                <span key={index} \r\n                                                                                                                    className=\"attachment_files\">\r\n                                                                                                                    {file.FileName} \r\n                                                                                                                    <em onClick={() => {\r\n                                                                                                                        const updated = [...fileAttachments];\r\n                                                                                                                        updated.splice(index, 1);\r\n                                                                                                                        setFileAttachments(updated);\r\n                                                                                                                    }}>X</em>\r\n                                                                                                                </span>\r\n                                                                                                            ))}\r\n                                                                                                        </div>\r\n                                                                                                    )}\r\n                                                                                                </div>\r\n                                                                                            )}\r\n                                                                                            {selected.Status.StatusID != 4 &&\r\n                                                                                                <button\r\n                                                                                                    type=\"button\"\r\n                                                                                                    className=\"btn btn-success\"\r\n                                                                                                    onClick={() => updateTicketRemarks(1)}\r\n                                                                                                >\r\n                                                                                                    Submit\r\n                                                                                                </button>\r\n                                                                                            }\r\n                                                                                        </div>\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </td>\r\n                                                            )}\r\n                                                        </tr>\r\n                                                    </tbody>\r\n                                                </table>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default MyTicketDetails;"], "mappings": ";;AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,6BAA6B;AAC/G,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,gBAAA;EAC1B,MAAM;IAAEC;EAAS,CAAC,GAAGd,SAAS,CAAC,CAAC;EAChC,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM6B,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;EACzE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC;IACrCoC,MAAM,EAAEC,SAAS;IACjBC,SAAS,EAAED,SAAS;IACpBE,YAAY,EAAEF,SAAS;IACvBG,MAAM,EAAE;MAAEC,QAAQ,EAAE;IAAK;EAC7B,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACf;IAAEC,QAAQ,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAW,CAAC,CAC1C;EAED,MAAMC,OAAO,GAAIC,GAAG,IAAK;IACrB,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACA,GAAG,CAACC,IAAI,CAAC,CAAC,IAAI,OAAOD,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI;EAC/F,CAAC;EAED,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG;MACZjC,QAAQ,EAAEA;IACd,CAAC;IAEDV,gBAAgB,CAAC2C,OAAO,CAAC,CACpBC,IAAI,CAAEC,QAAQ,IAAK;MAChB,IAAIA,QAAQ,EAAE;QACVjC,gBAAgB,CAACiC,QAAQ,CAAC;QAC1B/B,cAAc,CAAC+B,QAAQ,CAACC,WAAW,IAAI,EAAE,CAAC;QAE1C,IAAID,QAAQ,CAACR,QAAQ,IAAI,CAAC,EAAE;UACxBD,UAAU,CAACW,IAAI,CAAC;YAAEV,QAAQ,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAS,CAAC,CAAC;QAC1D;QAEAT,WAAW,CAACmB,IAAI,KAAK;UACjB,GAAGA,IAAI;UACPlB,MAAM,EAAE;YAAEO,QAAQ,EAAEQ,QAAQ,CAACR;UAAS,CAAC;UACvCL,SAAS,EAAE;YAAEiB,OAAO,EAAEJ,QAAQ,CAACK;UAAQ;QAC3C,CAAC,CAAC,CAAC;MACP,CAAC,MAAM;QACHtC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,cAAc,CAAC,EAAE,CAAC;QAClBe,WAAW,CAAC;UACRC,MAAM,EAAEC,SAAS;UACjBC,SAAS,EAAED,SAAS;UACpBE,YAAY,EAAEF,SAAS;UACvBG,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAK;QAC7B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CACDgB,KAAK,CAAC,MAAM;MACTvC,gBAAgB,CAAC,EAAE,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClBe,WAAW,CAAC;QACRC,MAAM,EAAEC,SAAS;QACjBC,SAAS,EAAED,SAAS;QACpBE,YAAY,EAAEF,SAAS;QACvBG,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAK;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC,CACDiB,OAAO,CAAC,MAAM;MACX9B,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;EACV,CAAC;EAED3B,SAAS,CAAC,MAAM;IACZ+C,uBAAuB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAAChC,QAAQ,CAAC,CAAC;EAGd,MAAM2C,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACH,KAAK,CAAC;IAC5C,MAAMI,QAAQ,GAAG,EAAE;IAEnBJ,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;MAClB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACnB,MAAMC,SAAS,GAAGD,CAAC,CAACP,MAAM,CAACS,MAAM;QACjCR,QAAQ,CAACZ,IAAI,CAAC;UACVqB,QAAQ,EAAEP,IAAI,CAACQ,IAAI;UACnBC,iBAAiB,EAAEC,IAAI,CAACL,SAAS,CAAC;UAClCM,aAAa,EAAE,EAAE;UACjBC,WAAW,EAAEZ,IAAI,CAACa;QACtB,CAAC,CAAC;QAEF,IAAIf,QAAQ,CAACgB,MAAM,KAAKpB,KAAK,CAACoB,MAAM,EAAE;UAClCzD,kBAAkB,CAACyC,QAAQ,CAAC;QAChC;MACJ,CAAC;MACDG,MAAM,CAACc,kBAAkB,CAACf,IAAI,CAAC;IACnC,CAAC,CAAC;EACN,CAAC;EAED,MAAMgB,mBAAmB,GAAIC,SAAS,IAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACvC,IAAI,CAAC7D,WAAW,KAAKoB,OAAO,CAACxB,WAAW,CAAC,IAAIA,WAAW,CAAC4D,MAAM,IAAI,EAAE,CAAC,EAAE;MACpE9E,KAAK,CAACoF,KAAK,CAAC1C,OAAO,CAACxB,WAAW,CAAC,GAAG,4BAA4B,GAAG,mCAAmC,CAAC;MACtG;IACJ;IAEA,IAAImE,cAAc,GAAG,CAAC;IACtB,IAAItD,QAAQ,CAACE,MAAM,CAACO,QAAQ,KAAK,CAAC,EAAE;MAChC6C,cAAc,GAAG,CAAC;IACtB;IACA,IAAI/D,WAAW,KAAK,CAAC,EAAE;MACnB+D,cAAc,GAAG,CAAC;IACtB;IAEA,MAAMC,WAAW,GAAG;MAChBC,QAAQ,EAAE1E,QAAQ;MAClB2E,QAAQ,EAAE,cAActE,WAAW,EAAE;MACrCuE,SAAS,GAAAP,qBAAA,GAAExD,WAAW,aAAXA,WAAW,wBAAAyD,sBAAA,GAAXzD,WAAW,CAAEgE,OAAO,CAAC,CAAC,CAAC,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBQ,KAAK,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,CAAC;MAC9C1C,QAAQ,EAAE6C,cAAc;MACxBO,SAAS,EAAEX,SAAS;MACpBY,OAAO,EAAE,EAAE;MACXtB,QAAQ,EAAE,EAAE;MACZuB,WAAW,EAAExE,WAAW;MACxByE,MAAM,EAAEjF,aAAa,CAACiF,MAAM;MAC5BC,QAAQ,EAAElF,aAAa,CAACkF,QAAQ;MAChCC,KAAK,EAAEnF,aAAa,CAACmF,KAAK;MAC1BC,OAAO,EAAEpF,aAAa,CAACoF,OAAO;MAC9BC,eAAe,EAAErF,aAAa,CAAC0B,QAAQ,KAAKT,QAAQ,CAACE,MAAM,CAACO;IAChE,CAAC;IAED,IAAIpB,eAAe,CAAC0D,MAAM,GAAG,CAAC,EAAE;MAE5BzE,UAAU,CAACe,eAAe,CAAC,CAC1B2B,IAAI,CAAEC,QAAQ,IAAK;QAChB,MAAMoD,eAAe,GAAGpD,QAAQ;QAChCsC,WAAW,CAACO,OAAO,GAAGO,eAAe,CAAC,CAAC,CAAC,CAACzB,aAAa;QACtDW,WAAW,CAACf,QAAQ,GAAG6B,eAAe,CAAC,CAAC,CAAC,CAAC7B,QAAQ;QAClDe,WAAW,CAACe,KAAK,GAAGD,eAAe,CAAC,CAAC,CAAC,CAACC,KAAK;QAE5CC,0BAA0B,CAAChB,WAAW,CAAC;MAC3C,CAAC,CAAC,CACDhC,KAAK,CAAC,MAAM;QACT;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACHgD,0BAA0B,CAAChB,WAAW,CAAC;IAC3C;EACJ,CAAC;EAED,MAAMgB,0BAA0B,GAAIhB,WAAW,IAAK;IAChDlF,mBAAmB,CAACkF,WAAW,CAAC,CAC/BvC,IAAI,CAAGC,QAAQ,IAAK;MACjB,IAAGA,QAAQ,EAAE;QACThD,KAAK,CAACuG,OAAO,CAAC,sBAAsB,CAAC;QACrClF,kBAAkB,CAAC,EAAE,CAAC;QACtBF,cAAc,CAAC,EAAE,CAAC;QAClB0B,uBAAuB,CAAC,CAAC;MAC7B,CAAC,MACI;QACD7C,KAAK,CAACoF,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACJ,CAAC,CAAC,CACD9B,KAAK,CAAC,MAAM,CAEb,CAAC,CAAC;EACN,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrC,MAAMpB,WAAW,GAAG;MAChB,OAAO,EAAEmB,KAAK;MACd,OAAO,EAAEC;IACb,CAAC;IAEDxG,cAAc,CAACoF,WAAW,CAAC,CAC1BvC,IAAI,CAAEC,QAAQ,IAAK;MAChB,MAAM2D,IAAI,GAAG3D,QAAQ;MACrB,IAAI2D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,SAAS,EAAE;QACjBC,MAAM,CAACC,IAAI,CAACH,IAAI,CAACC,SAAS,EAAE,QAAQ,CAAC;MACzC;IACJ,CAAC,CAAC,CACDtD,KAAK,CAAC,MAAM,CAEb,CAAC,CAAC;EACN,CAAC;EAED,MAAMyD,YAAY,GAAIC,GAAG,IAAK;IAC1B,IAAIA,GAAG,EAAE;MACLH,MAAM,CAACC,IAAI,CAACE,GAAG,EAAE,QAAQ,CAAC;IAC9B;EACJ,CAAC;EAED,IAAIxF,SAAS,EAAE;IACX,oBAAOjB,OAAA;MAAA0G,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChC;EAEA,oBACI9G,OAAA;IAAK+G,SAAS,EAAC,+BAA+B;IAAAL,QAAA,gBAC1C1G,OAAA;MAAKgH,KAAK,EAAC,cAAc;MAAAN,QAAA,eACrB1G,OAAA;QAAKgH,KAAK,EAAC,KAAK;QAAAN,QAAA,eACZ1G,OAAA;UAAKgH,KAAK,EAAC,6BAA6B;UAAAN,QAAA,eACpC1G,OAAA;YAAKgH,KAAK,EAAC,cAAc;YAAAN,QAAA,eACrB1G,OAAA;cAAGgH,KAAK,EAAC,aAAa;cAAAN,QAAA,gBAClB1G,OAAA;gBAAMgH,KAAK,EAAC,WAAW;gBAAAN,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5C9G,OAAA;gBAAQsE,IAAI,EAAC,QAAQ;gBAAC0C,KAAK,EAAC,yBAAyB;gBAAAN,QAAA,GAAE,CAAAnG,aAAa,aAAbA,aAAa,wBAAAJ,qBAAA,GAAbI,aAAa,CAAE0G,eAAe,cAAA9G,qBAAA,uBAA9BA,qBAAA,CAAgC+G,IAAI,KAAI,cAAc,EACxG,CAAA3G,aAAa,aAAbA,aAAa,wBAAAH,sBAAA,GAAbG,aAAa,CAAE0G,eAAe,cAAA7G,sBAAA,uBAA9BA,sBAAA,CAAgC+G,UAAU,KACvC,IAAI5G,aAAa,CAAC0G,eAAe,CAACE,UAAU,GAAG;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN9G,OAAA;MAAKgH,KAAK,EAAC,cAAc;MAAAN,QAAA,eACrB1G,OAAA;QAAKgH,KAAK,EAAC,WAAW;QAAAN,QAAA,eAClB1G,OAAA;UAAKgH,KAAK,EAAC,MAAM;UAAAN,QAAA,eACb1G,OAAA;YAAKgH,KAAK,EAAC,YAAY;YAAAN,QAAA,eACnB1G,OAAA;cAAKgH,KAAK,EAAC,2BAA2B;cAAAN,QAAA,eAClC1G,OAAA;gBAAKgH,KAAK,EAAC,uBAAuB;gBAAAN,QAAA,eAC9B1G,OAAA;kBAAKgH,KAAK,EAAC,2BAA2B;kBAAAN,QAAA,eAClC1G,OAAA;oBAAKgH,KAAK,EAAC,sBAAsB;oBAAAN,QAAA,eAC7B1G,OAAA;sBAAK+G,SAAS,EAAC,kBAAkB;sBAAAL,QAAA,eAC7B1G,OAAA;wBAAO+G,SAAS,EAAC,aAAa;wBAAAL,QAAA,gBAC1B1G,OAAA;0BAAA0G,QAAA,eACI1G,OAAA;4BAAA0G,QAAA,gBACI1G,OAAA;8BAAA0G,QAAA,EAAI;4BAAU;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnB9G,OAAA;8BAAA0G,QAAA,EAAI;4BAAS;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClB9G,OAAA;8BAAA0G,QAAA,EAAI;4BAAO;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAChB9G,OAAA;8BAAA0G,QAAA,EAAI;4BAAQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACjB9G,OAAA;8BAAA0G,QAAA,EAAI;4BAAM;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACf9G,OAAA;8BAAA0G,QAAA,EAAI;4BAAe;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACR9G,OAAA;0BAAA0G,QAAA,gBACI1G,OAAA;4BAAIgH,KAAK,EAAC,mBAAmB;4BAAAN,QAAA,gBACzB1G,OAAA;8BAAA0G,QAAA,EAAKnG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6G;4BAAe;8BAAAT,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACzC9G,OAAA;8BAAA0G,QAAA,EAAKhH,UAAU,CAACa,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8G,SAAS;4BAAC;8BAAAV,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAC/C9G,OAAA;8BAAA0G,QAAA,EAAKnG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+G;4BAAO;8BAAAX,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACjC9G,OAAA;8BAAA0G,QAAA,EAAKnG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgH;4BAAW;8BAAAZ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACrC9G,OAAA;8BAAA0G,QAAA,EAAKnG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiH;4BAAY;8BAAAb,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACtC9G,OAAA;8BAAA0G,QAAA,EAAKhH,UAAU,CAACa,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkH,SAAS;4BAAC;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACL9G,OAAA;4BAAA0G,QAAA,EACKjG,WAAW,IAAIA,WAAW,CAAC8D,MAAM,GAAG,CAAC,iBAClCvE,OAAA;8BAAI0H,OAAO,EAAE,CAAE;8BAACX,SAAS,EAAC,eAAe;8BAAAL,QAAA,eACrC1G,OAAA;gCAAK+G,SAAS,EAAC,gBAAgB;gCAAAL,QAAA,eAC3B1G,OAAA;kCAAK+G,SAAS,EAAC,mBAAmB;kCAAAL,QAAA,GAC7BjG,WAAW,CAACkH,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;oCAAA,IAAAC,aAAA,EAAAC,cAAA;oCAAA,OAC5B,CAACH,OAAO,CAACvC,SAAS,IAAI,CAAC,IAAIuC,OAAO,CAACvC,SAAS,IAAI,CAAC,kBAE7CrF,OAAA;sCAAiB+G,SAAS,EAAE,6BAA6Ba,OAAO,CAACvC,SAAS,KAAK,CAAC,GAAG,OAAO,GAAGuC,OAAO,CAACvC,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,EAAG;sCAAAqB,QAAA,eACjI1G,OAAA;wCAAKgI,KAAK,EAAE;0CAAEC,MAAM,EAAE;wCAAa,CAAE;wCAAAvB,QAAA,gBACjC1G,OAAA;0CAAM+G,SAAS,EAAC,MAAM;0CAAAL,QAAA,GAAC,SACZ,eAAA1G,OAAA;4CAAA0G,QAAA,IAAAoB,aAAA,GAAIF,OAAO,CAACM,IAAI,cAAAJ,aAAA,uBAAZA,aAAA,CAAcK,QAAQ,EAAC,GAAC,GAAAJ,cAAA,GAACH,OAAO,CAACM,IAAI,cAAAH,cAAA,uBAAZA,cAAA,CAAcK,UAAU,EAAC,GAAC;0CAAA;4CAAAzB,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAG,CAAC;wCAAA;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAChE,CAAC,eACP9G,OAAA;0CAAK+G,SAAS,EAAC,eAAe;0CAAAL,QAAA,eAC1B1G,OAAA;4CAAM+G,SAAS,EAAC,SAAS;4CAAAL,QAAA,EAAEhH,UAAU,CAACkI,OAAO,CAACP,SAAS;0CAAC;4CAAAV,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAO;wCAAC;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAC/D,CAAC,eACN9G,OAAA;0CAAKgI,KAAK,EAAE;4CAAEK,KAAK,EAAE,MAAM;4CAAEC,OAAO,EAAE;0CAAQ,CAAE;0CAAA5B,QAAA,gBAC5C1G,OAAA;4CAAA0G,QAAA,EAAIkB,OAAO,CAACW;0CAAO;4CAAA5B,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAI,CAAC,EACvBc,OAAO,CAACtC,OAAO,IAAIsC,OAAO,CAACtC,OAAO,KAAK,CAAC,CAAC,IAAIsC,OAAO,CAACtC,OAAO,CAACkD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,iBACjFxI,OAAA;4CACIgI,KAAK,EAAE;8CAACS,MAAM,EAAE,SAAS;8CAAEC,cAAc,EAAE,WAAW;8CAAEC,KAAK,EAAE;4CAAS,CAAE;4CAC1EC,OAAO,EAAEA,CAAA,KAAM3C,cAAc,CAAC2B,OAAO,CAACtC,OAAO,EAAEsC,OAAO,CAAC9B,KAAK,CAAE;4CAAAY,QAAA,EAC7DkB,OAAO,CAAC5D;0CAAQ;4CAAA2C,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAClB,CAAE,EAERc,OAAO,CAACtC,OAAO,IAAIsC,OAAO,CAACtC,OAAO,KAAK,CAAC,CAAC,IAAIsC,OAAO,CAACtC,OAAO,CAACkD,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,iBACjFxI,OAAA;4CACIgI,KAAK,EAAE;8CAACS,MAAM,EAAE,SAAS;8CAAEC,cAAc,EAAE,WAAW;8CAAEC,KAAK,EAAE;4CAAS,CAAE;4CAC1EC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAACoB,OAAO,CAACtC,OAAO,CAAE;4CAAAoB,QAAA,EAE5CnG,aAAa,CAACsI;0CAAW;4CAAAlC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAC3B,CAAE;wCAAA;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAER,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACL;oCAAC,GA1BAe,KAAK;sCAAAlB,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OA2BV,CACR;kCAAA,CACJ,CAAC,eACF9G,OAAA;oCAAK+G,SAAS,EAAC,sBAAsB;oCAAAL,QAAA,eACjC1G,OAAA;sCAAK+G,SAAS,EAAC,kBAAkB;sCAAAL,QAAA,eAC7B1G,OAAA;wCAAK+G,SAAS,EAAC,kBAAkB;wCAAAL,QAAA,gBAC7B1G,OAAA;0CAAA2G,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAK,CAAC,eACN9G,OAAA;0CAAK+G,SAAS,EAAC,eAAe;0CAAAL,QAAA,eAC1B1G,OAAA;4CAAA0G,QAAA,EAAI;0CAAW;4CAAAC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAI;wCAAC;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OACnB,CAAC,eACN9G,OAAA;0CACI+G,SAAS,EAAC,mBAAmB;0CAC7BiB,KAAK,EAAE;4CAAEK,KAAK,EAAE;0CAAO,CAAE;0CACzBS,IAAI,EAAC,IAAI;0CACTC,KAAK,EAAEpI,WAAY;0CACnBqI,QAAQ,EAAGnF,CAAC,IAAKjD,cAAc,CAACiD,CAAC,CAACP,MAAM,CAACyF,KAAK,CAAE;0CAChDE,WAAW,EAAC;wCAAuB;0CAAAtC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OACtC,CAAC,eACF9G,OAAA;0CAAK+G,SAAS,EAAC,8BAA8B;0CAAAL,QAAA,GACxClF,QAAQ,CAACE,MAAM,CAACO,QAAQ,KAAK,CAAC,iBAC3BjC,OAAA;4CAAKgI,KAAK,EAAE;8CAAEkB,UAAU,EAAE,MAAM;8CAAEjB,MAAM,EAAE;4CAAM,CAAE;4CAAAvB,QAAA,GAAC,mHAEjB,eAAA1G,OAAA;8CAAA2G,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAAK,CAAC,eAAA9G,OAAA;8CAAA2G,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAAK,CAAC,eAC1C9G,OAAA;8CAAK+G,SAAS,EAAC,8BAA8B;8CAAAL,QAAA,gBACzC1G,OAAA;gDACIsE,IAAI,EAAC,OAAO;gDACZyC,SAAS,EAAC,kBAAkB;gDAC5BoC,EAAE,EAAC,cAAc;gDACjBlF,IAAI,EAAC,WAAW;gDAChB8E,KAAK,EAAC,GAAG;gDACTK,OAAO,EAAErI,WAAW,KAAK,CAAE;gDAC3BiI,QAAQ,EAAGnF,CAAC,IAAK7C,cAAc,CAACqI,MAAM,CAACxF,CAAC,CAACP,MAAM,CAACyF,KAAK,CAAC;8CAAE;gDAAApC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAC3D,CAAC,eACF9G,OAAA;gDAAO+G,SAAS,EAAC,kBAAkB;gDAACuC,OAAO,EAAC,cAAc;gDAAA5C,QAAA,EAAC;8CAAG;gDAAAC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAAO,CAAC;4CAAA;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACrE,CAAC,eACN9G,OAAA;8CAAK+G,SAAS,EAAC,8BAA8B;8CAAAL,QAAA,gBACzC1G,OAAA;gDACIsE,IAAI,EAAC,OAAO;gDACZyC,SAAS,EAAC,kBAAkB;gDAC5BoC,EAAE,EAAC,aAAa;gDAChBlF,IAAI,EAAC,WAAW;gDAChB8E,KAAK,EAAC,GAAG;gDACTK,OAAO,EAAErI,WAAW,KAAK,CAAE;gDAC3BiI,QAAQ,EAAGnF,CAAC,IAAK7C,cAAc,CAACqI,MAAM,CAACxF,CAAC,CAACP,MAAM,CAACyF,KAAK,CAAC;8CAAE;gDAAApC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAC3D,CAAC,eACF9G,OAAA;gDAAO+G,SAAS,EAAC,kBAAkB;gDAACuC,OAAO,EAAC,aAAa;gDAAA5C,QAAA,EAAC;8CAAE;gDAAAC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAAO,CAAC;4CAAA;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACnE,CAAC;0CAAA;4CAAAH,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OACL,CACR,eACD9G,OAAA;4CAAKgI,KAAK,EAAE;8CAAEM,OAAO,EAAE,MAAM;8CAAED,KAAK,EAAE,MAAM;8CAAEkB,OAAO,EAAE;4CAAa,CAAE;4CAAA7C,QAAA,gBAClE1G,OAAA;8CAAA0G,QAAA,EAAO;4CAAa;8CAAAC,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAAO,CAAC,eAC5B9G,OAAA;8CACI+G,SAAS,EAAC,cAAc;8CACxBgC,KAAK,EAAE,EAAA1I,gBAAA,GAAAmB,QAAQ,CAACE,MAAM,cAAArB,gBAAA,uBAAfA,gBAAA,CAAiB4B,QAAQ,KAAI,EAAG;8CACvC+G,QAAQ,EAAGnF,CAAC,IAAKpC,WAAW,CAACmB,IAAI,KAAK;gDAClC,GAAGA,IAAI;gDACPlB,MAAM,EAAE;kDAAEO,QAAQ,EAAEuH,QAAQ,CAAC3F,CAAC,CAACP,MAAM,CAACyF,KAAK;gDAAE;8CACjD,CAAC,CAAC,CAAE;8CACJU,QAAQ,EAAE,KAAM,CAAC;8CAAA;8CACjBzB,KAAK,EAAE;gDAAEM,OAAO,EAAE,cAAc;gDAAED,KAAK,EAAE,MAAM;gDAAEJ,MAAM,EAAE;8CAAY,CAAE;8CAAAvB,QAAA,gBAEvE1G,OAAA;gDAAQ+I,KAAK,EAAC,EAAE;gDAAArC,QAAA,EAAC;8CAAa;gDAAAC,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAAQ,CAAC,EACtC9E,UAAU,CACN0H,MAAM,CAAC,CAACC,MAAM,EAAE9B,KAAK,EAAE+B,IAAI,KACxB/B,KAAK,KAAK+B,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC7H,QAAQ,KAAK0H,MAAM,CAAC1H,QAAQ,CAAC,CAAC,CACjE8H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,QAAQ,IAAI,CAAC,KAAKD,CAAC,CAACC,QAAQ,IAAI,CAAC,CAAC,CAAC,CACrDvC,GAAG,CAACgC,MAAM,iBACP3J,OAAA;gDAA8B+I,KAAK,EAAEY,MAAM,CAAC1H,QAAS;gDAAAyE,QAAA,EAChDiD,MAAM,CAACzH;8CAAU,GADTyH,MAAM,CAAC1H,QAAQ;gDAAA0E,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAEpB,CACX,CAAC;4CAAA;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAEF,CAAC;0CAAA;4CAAAH,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OACR,CAAC,EACLtF,QAAQ,CAACE,MAAM,CAACO,QAAQ,IAAI,CAAC,iBAC1BjC,OAAA;4CAAK+G,SAAS,EAAC,qBAAqB;4CAAAL,QAAA,gBAChC1G,OAAA;8CACIsE,IAAI,EAAC,MAAM;8CACX6E,EAAE,EAAC,QAAQ;8CACXpC,SAAS,EAAC,8BAA8B;8CACxCiC,QAAQ,EAAE/F,gBAAiB;8CAC3BkH,QAAQ;8CACRC,MAAM,EAAC;4CAAiB;8CAAAzD,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OAC3B,CAAC,eACF9G,OAAA;8CAAOsJ,OAAO,EAAC,QAAQ;8CAACvC,SAAS,EAAC,aAAa;8CAACiB,KAAK,EAAE;gDAAEM,OAAO,EAAE;8CAAS,CAAE;8CAAA5B,QAAA,eACzE1G,OAAA;gDAAKqK,KAAK,EAAC,4BAA4B;gDAAChC,KAAK,EAAC,IAAI;gDAACiC,MAAM,EAAC,IAAI;gDAACC,OAAO,EAAC,WAAW;gDAAA7D,QAAA,eAC9E1G,OAAA;kDAAMwK,CAAC,EAAC;gDAA4P;kDAAA7D,QAAA,EAAAC,YAAA;kDAAAC,UAAA;kDAAAC,YAAA;gDAAA,OAAE;8CAAC;gDAAAH,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OACtQ;4CAAC;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACH,CAAC,EACPjG,eAAe,CAAC0D,MAAM,GAAG,CAAC,iBACvBvE,OAAA;8CAAK+G,SAAS,EAAC,MAAM;8CAAAL,QAAA,EAChB7F,eAAe,CAAC8G,GAAG,CAAC,CAAClE,IAAI,EAAEoE,KAAK,kBAC7B7H,OAAA;gDACI+G,SAAS,EAAC,kBAAkB;gDAAAL,QAAA,GAC3BjD,IAAI,CAACO,QAAQ,eACdhE,OAAA;kDAAI4I,OAAO,EAAEA,CAAA,KAAM;oDACf,MAAM6B,OAAO,GAAG,CAAC,GAAG5J,eAAe,CAAC;oDACpC4J,OAAO,CAACC,MAAM,CAAC7C,KAAK,EAAE,CAAC,CAAC;oDACxB/G,kBAAkB,CAAC2J,OAAO,CAAC;kDAC/B,CAAE;kDAAA/D,QAAA,EAAC;gDAAC;kDAAAC,QAAA,EAAAC,YAAA;kDAAAC,UAAA;kDAAAC,YAAA;gDAAA,OAAI,CAAC;8CAAA,GAPFe,KAAK;gDAAAlB,QAAA,EAAAC,YAAA;gDAAAC,UAAA;gDAAAC,YAAA;8CAAA,OAQV,CACT;4CAAC;8CAAAH,QAAA,EAAAC,YAAA;8CAAAC,UAAA;8CAAAC,YAAA;4CAAA,OACD,CACR;0CAAA;4CAAAH,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OACA,CACR,EACAtF,QAAQ,CAACE,MAAM,CAACO,QAAQ,IAAI,CAAC,iBAC1BjC,OAAA;4CACIsE,IAAI,EAAC,QAAQ;4CACbyC,SAAS,EAAC,iBAAiB;4CAC3B6B,OAAO,EAAEA,CAAA,KAAMnE,mBAAmB,CAAC,CAAC,CAAE;4CAAAiC,QAAA,EACzC;0CAED;4CAAAC,QAAA,EAAAC,YAAA;4CAAAC,UAAA;4CAAAC,YAAA;0CAAA,OAAQ,CAAC;wCAAA;0CAAAH,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAEZ,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACL;oCAAC;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACL;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACL;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BACP;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC5G,EAAA,CA5ZID,eAAe;EAAA,QACIT,SAAS;AAAA;AAAAmL,EAAA,GAD5B1K,eAAe;AA8ZrB,eAAeA,eAAe;AAAC,IAAA0K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}