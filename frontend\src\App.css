@import url("https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700");

* {
  box-sizing: border-box;
}

.App {
  text-align: center;
}

main {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Common form styles */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 10px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f4f7f6;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

.app {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  padding: 20px;
  background-color: #fff;
}

/* Toast notifications */
.Toastify__toast {
  border-radius: 4px;
}

.Toastify__toast--success {
  background-color: #5cb85c;
}

.Toastify__toast--error {
  background-color: #d9534f;
}

/* Animation classes */
.animated {
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

.fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.show {
  display: block !important;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.main-header {
  background-color: #343a40;
  padding: 0;
  margin: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  max-width: 1140px;
  margin: 0 auto;
}

.welcome {
  color: #fff;
  font-size: 14px;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-links a {
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  padding: 5px 0;
}

.nav-links a:hover {
  color: rgba(255, 255, 255, 0.8);
}

.container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 20px;
}

/* Modern Dashboard Styles for MainLayout.js */
.modern-layout {
  min-height: 100vh;
  background: #c0d2e6;
}

.grid-layout {
  display: grid;
  grid-template-columns: 290px 1fr;
  grid-template-rows: 5px 1fr;
  grid-template-areas: "header header"
    "sidebar main";
  height: 100vh;
  gap: 10px 0px;
  background: #c0d2e6;
}

/* .grid-header {
  grid-area: header;
  background-color: #fff;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 15px 25px 0 25px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  min-height: 60px;
} */

.grid-sidebar {
  grid-area: sidebar;
  background-color: transparent;
  overflow: hidden;
  z-index: 999;
  margin-left: 15px;
  margin-bottom: 15px;
}


.modern-main-content {
  grid-row: 2;
  grid-column: 2;
  height: 96vh;
  flex: 1 1;
  background: #f7f8fa;
  border-radius: 20px;
  width: 100%;
  min-height: 90vh;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
  overflow: auto;
  transition: margin 0.3s;
}

.modern-sidebar {
     grid-row: 2;
    grid-column: 1;
    background: #2b2a52;
    position: static;
    min-height: 92vh;
    height: 96vh;
    border-radius: 24px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    color: #fff;
    padding: 32px 10px 18px 10px;
    display: block;
    margin: 0px 0px 0px 12px;
}

.modern-user-card {
  width: 100%;
  text-align: center;
  margin-bottom: 18px;
}

.modern-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #232526;
  margin: 0 auto 12px auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modern-username {
  font-weight: 700;
  font-size: 1.2rem;
  margin-bottom: 2px;
}

.modern-welcome {
  font-size: 0.95rem;
  color: #b0b0b0;
}

.modern-nav-tabs {
  display: flex;
  width: 100%;
  margin-bottom: 24px;
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.08);
}

.modern-nav-link {
  flex: 1 1;
  padding: 12px 0;
  text-align: center;
  color: #b0b0b0;
  display: flex;
  background: transparent;
  font-weight: 600;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  text-decoration: none;
  align-items: center;
  justify-content: center;
}

.modern-nav-link.active {
  background: rgba(0, 0, 0, 0.18);
  color: #fff;
}

.modern-menu-section {
  width: 100%;
  margin-top: 18px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.04);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 18px 0;
}

.modern-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  color: #fff;
  border-radius: 12px;
  margin: 6px 0;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: background 0.2s, color 0.2s;
}

.modern-menu-item:hover,
.modern-menu-item.active {
  background: rgba(255, 255, 255, 0.10);
  color: #00e6d0;
}


/* Modern Table Styles */
.table-responsive {
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(31, 38, 135, 0.08);
  background: #fff;
  padding: 0;
  margin: 0 auto 32px auto;
  overflow-x: auto;
}

.table {
  border-radius: 16px;
  overflow: hidden;
  background: #fff;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 700px;
  font-size: 1rem;
  color: #232526;
}

.table th {
  background: #232526;
  color: #fff;
  font-weight: 700;
  padding: 16px 18px;
  border: none;
  position: sticky;
  top: 0;
  z-index: 2;
  letter-spacing: 0.03em;
}

.table td {
  padding: 14px 18px;
  border: none;
  background: #fff;
  transition: background 0.2s;
}

.table tbody tr {
  border-bottom: 1px solid #f0f0f0;
  transition: box-shadow 0.2s, background 0.2s;
}

.table tbody tr:nth-child(even) {
  background: #f7f8fa;
}

.table tbody tr:hover {
  background: #eaf6ff;
  box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.06);
}

.table th:first-child,
.table td:first-child {
  border-top-left-radius: 16px;
}

.table th:last-child,
.table td:last-child {
  border-top-right-radius: 16px;
}

@media (max-width: 900px) {
  .table {
    min-width: 500px;
    font-size: 0.95rem;
  }

  .table th,
  .table td {
    padding: 10px 8px;
  }

  .grid-layout {
    grid-template-columns: 1fr;
    grid-template-rows: 60px auto 1fr;
    grid-template-areas:
      "header"
      "sidebar"
      "main";
    gap: 10px 0;
    height: auto;
  }

  .grid-header {
    margin: 10px 10px 0 10px;
    min-height: 50px;
    padding: 0 10px;
  }

  .grid-sidebar {
    margin: 0 10px 10px 10px;
  }

  .grid-main {
    margin: 0 10px 10px 10px;
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .table {
    min-width: 350px;
    font-size: 0.92rem;
  }

  .table th,
  .table td {
    padding: 8px 4px;
  }
}