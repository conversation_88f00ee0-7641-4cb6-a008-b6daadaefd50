import React, { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Container,
    Grid2 as Grid,
    Typography,
    TextField,
    MenuItem,
    InputLabel,
    Select,
    FormControl,
    IconButton,
    Chip,
    Stack,
    Paper,
    Fade,
    Grow
} from '@mui/material';
import { 
    CloudUpload as CloudUploadIcon,
    Send as SendIcon,
    AttachFile as AttachFileIcon,
    Feedback as FeedbackIcon,
    TrendingUp as TrendingUpIcon,
    Link as LinkIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import '../styles/scss/main.scss';
import {
    CreateNewTicket,
    GetAllIssueSubIssue,
    GetProcessMasterByAPI,
    UploadFile
} from '../services/feedbackService';

const CreateFeedback = () => {
    const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm();
    const navigate = useNavigate();
    const fileInputRef = useRef();
    const [selectedFiles, setSelectedFiles] = useState([]);
    const [isDisabled, setIsDisabled] = useState(false);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [source, setSource] = useState([]);
    const selectedProcess = watch('process');
    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const productOptions = [
        { value: 0, label: 'Select' },
        { value: 115, label: 'Investment' },
        { value: 7, label: 'Term' },
        { value: 2, label: 'Health' },
        { value: 117, label: 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    setSource(data);
                    GetAllIssueSubIssueService();
                }
            })
            .catch(() => {
                setSource([]);
                setIssueSubIssue([]);
            })
    };

    const GetAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    let filteredData = data;

                    if (userDetails.EMPData[0].BU !== 7 && userDetails.EMPData[0].BU !== 115) {
                        filteredData = data.filter(item => item.IssueID !== 42);
                    }
                    filteredData = filteredData.filter(item => item.IssueID !== 41);

                    setIssueSubIssue(filteredData);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            })
    };

    const handleFileChange = (event) => {
        const files = Array.from(event.target.files);
        const fileData = [];

        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const binaryStr = e.target.result;
                fileData.push({
                    FileName: file.name,
                    AttachemntContent: btoa(binaryStr),
                    AttachmentURL: "",
                    ContentType: file.type
                });

                if (fileData.length === files.length) {
                    setSelectedFiles(fileData);
                }
            };
            reader.readAsBinaryString(file);
        });
    };

    const removeFile = (index) => {
        setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    };

    const validateForm = (data) => {
        if (!data.process) {
            toast.error('Please select Process');
            return false;
        }
        if (!data.feedback || data.feedback === '0') {
            toast.error('Please select Feedback Type');
            return false;
        }
        if ([2, 4, 5, 8].includes(data.process) && (!data.product || data.product === '0')) {
            toast.error('Please select Product');
            return false;
        }
        if (!data.description || data.description.length <= 10) {
            toast.error('Query should be more than 10 char');
            return false;
        }
        return true;
    };

    const onSubmit = async (data) => {
        if (!validateForm(data)) {
            return;
        }

        setIsDisabled(true);

        var Title = source.find(s => s.SourceID === data.process)?.Name;
        if (data.feedback != 0) {
            Title = issueSubIssue.find(i => i.IssueID === data.feedback)?.ISSUENAME;
        }

        const requestBody = {
            CreatedBy: userDetails?.EMPData[0]?.EmpID,
            SourceID: data.process,
            IssueID: data.feedback,
            Title: Title,
            Comments: `Comments : ${data.description}`,
            LeadID: data.leadId || '',
            ParentID: data.parentId || '',
            FileURL: '',
            FileName: '',
            ProductID: [2, 4, 5, 8].includes(data.process) ? data.product : 0,
            PayID: data.payId || '',
            OrderID: data.orderId || ''
        };

        if (selectedFiles.length > 0) {
            UploadFile(selectedFiles)
                .then((response) => {
                    const FileAttachments = response;
                    requestBody.FileURL = FileAttachments[0].AttachmentURL;
                    requestBody.FileName = FileAttachments[0].FileName;
                    requestBody.RefId = FileAttachments[0].RefId;

                    CreateNewTicketService(requestBody);
                })
                .catch(() => {
                    toast('Error uploading file. Please try again.', { type: 'error' });
                    setIsDisabled(false);
                })
        } else {
            CreateNewTicketService(requestBody);
        }
    };

    const CreateNewTicketService = (requestBody) => {
        CreateNewTicket(requestBody)
            .then((result) => {
                if (result?.Status > 0) {
                    toast.success('Ticket created successfully.');
                    navigate(`/MyTicketDetails/${result.Status}`);
                }
            })
            .catch(() => { })
            .finally(() => {
                setIsDisabled(false);
            })
    }

    return (
        <Box
            sx={{
                minHeight: '100vh',
                backgroundColor: '#f8fafc',
                py: 4,
                px: 2
            }}
        >
            <Container maxWidth="md">
                <Fade in timeout={800}>
                    <Box>
                        {/* Header Section */}
                        <div className="feedback-header">
                            {/* Decorative Elements */}
                            <div className="feedback-header-decoration-1"></div>
                            <div className="feedback-header-decoration-2"></div>
                            
                            <div className="feedback-header-content">
                                <div className="feedback-header-icon">
                                    <FeedbackIcon />
                                </div>
                                
                                <div className="feedback-header-text">
                                    <Typography variant="h5" className="feedback-header-title">
                                        Raise Feedback
                                        <span className="feedback-header-badge">NEW</span>
                                    </Typography>
                                    <Typography variant="body2" className="feedback-header-subtitle">
                                        Report issues with system, hardware, internet, or admin support
                                    </Typography>
                                    
                                    <div className="feedback-header-actions">
                                        <button className="feedback-quick-report-btn">
                                            <TrendingUpIcon fontSize="small" />
                                            Quick Report
                                        </button>
                                        <a 
                                            href="https://pbsupport.policybazaar.com/"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="feedback-support-btn"
                                        >
                                            <LinkIcon fontSize="small" />
                                            Support Portal
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/* Form Card */}
                        <Grow in timeout={1000}>
                            <Card
                                elevation={0}
                                sx={{
                                    borderRadius: 3,
                                    backgroundColor: 'white',
                                    border: '1px solid #e2e8f0',
                                    boxShadow: '0 10px 40px rgba(0,0,0,0.08)',
                                }}
                            >
                                <CardContent sx={{ p: 4 }}>
                                    <form onSubmit={handleSubmit(onSubmit)}>
                                        <Grid container spacing={3}>
                                            {/* Process Selection */}
                                            <Grid size={{ xs: 12, md: 6 }}>
                                                <FormControl fullWidth>
                                                    <InputLabel
                                                        sx={{
                                                            color: '#475569',
                                                            fontWeight: 500,
                                                            '&.Mui-focused': { color: '#3b82f6' }
                                                        }}
                                                    >
                                                        Process *
                                                    </InputLabel>
                                                    <Select
                                                        {...register('process')}
                                                        label="Process *"
                                                        onChange={e => setValue('process', e.target.value)}
                                                        defaultValue=""
                                                        sx={{
                                                            '& .MuiOutlinedInput-root': {
                                                                borderRadius: 2,
                                                                '& fieldset': {
                                                                    borderColor: '#d1d5db',
                                                                },
                                                                '&:hover fieldset': {
                                                                    borderColor: '#3b82f6',
                                                                },
                                                                '&.Mui-focused fieldset': {
                                                                    borderColor: '#3b82f6',
                                                                    borderWidth: 2,
                                                                },
                                                            }
                                                        }}
                                                    >
                                                        {source.map(src => (
                                                            <MenuItem key={src.SourceID} value={src.SourceID}>
                                                                {src.Name}
                                                            </MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Feedback Selection */}
                                            <Grid size={{ xs: 12, md: 6 }}>
                                                <FormControl fullWidth>
                                                    <InputLabel
                                                        sx={{
                                                            color: '#475569',
                                                            fontWeight: 500,
                                                            '&.Mui-focused': { color: '#3b82f6' }
                                                        }}
                                                    >
                                                        Feedback Type *
                                                    </InputLabel>
                                                    <Select
                                                        {...register('feedback')}
                                                        label="Feedback Type *"
                                                        onChange={e => setValue('feedback', e.target.value)}
                                                        defaultValue=""
                                                        sx={{
                                                            '& .MuiOutlinedInput-root': {
                                                                borderRadius: 2,
                                                                '& fieldset': {
                                                                    borderColor: '#d1d5db',
                                                                },
                                                                '&:hover fieldset': {
                                                                    borderColor: '#3b82f6',
                                                                },
                                                                '&.Mui-focused fieldset': {
                                                                    borderColor: '#3b82f6',
                                                                    borderWidth: 2,
                                                                },
                                                            }
                                                        }}
                                                    >
                                                        {issueSubIssue
                                                            .filter(issue => issue.SourceID === selectedProcess)
                                                            .map(issue => (
                                                                <MenuItem key={issue.IssueID} value={issue.IssueID}>
                                                                    {issue.ISSUENAME}
                                                                </MenuItem>
                                                            ))
                                                        }
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Product Selection (Conditional) */}
                                            {selectedProcess && [2, 4, 5, 8].includes(selectedProcess) && (
                                                <Grid size={{ xs: 12, md: 6 }}>
                                                    <FormControl fullWidth>
                                                        <InputLabel
                                                            sx={{
                                                                color: '#475569',
                                                                fontWeight: 500,
                                                                '&.Mui-focused': { color: '#3b82f6' }
                                                            }}
                                                        >
                                                            Product *
                                                        </InputLabel>
                                                        <Select
                                                            {...register('product')}
                                                            label="Product *"
                                                            onChange={e => setValue('product', e.target.value)}
                                                            defaultValue=""
                                                            sx={{
                                                                '& .MuiOutlinedInput-root': {
                                                                    borderRadius: 2,
                                                                    '& fieldset': {
                                                                        borderColor: '#d1d5db',
                                                                    },
                                                                    '&:hover fieldset': {
                                                                        borderColor: '#3b82f6',
                                                                    },
                                                                    '&.Mui-focused fieldset': {
                                                                        borderColor: '#3b82f6',
                                                                        borderWidth: 2,
                                                                    },
                                                                }
                                                            }}
                                                        >
                                                            {productOptions.map(opt => (
                                                                <MenuItem key={opt.value} value={opt.value}>
                                                                    {opt.label}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                    </FormControl>
                                                </Grid>
                                            )}

                                            {/* Description */}
                                            <Grid size={{ xs: 12 }}>
                                                <TextField
                                                    {...register('description')}
                                                    label="Description *"
                                                    multiline
                                                    rows={5}
                                                    fullWidth
                                                    variant="outlined"
                                                    placeholder="Please provide detailed description of your feedback..."
                                                    sx={{
                                                        '& .MuiInputBase-root': {
                                                            borderRadius: 2,
                                                        },
                                                        '& .MuiOutlinedInput-root': {
                                                            '& fieldset': {
                                                                borderColor: '#d1d5db',
                                                            },
                                                            '&:hover fieldset': {
                                                                borderColor: '#3b82f6',
                                                            },
                                                            '&.Mui-focused fieldset': {
                                                                borderColor: '#3b82f6',
                                                                borderWidth: 2,
                                                            },
                                                        },
                                                        '& .MuiInputLabel-root': {
                                                            color: '#475569',
                                                            fontWeight: 500,
                                                            '&.Mui-focused': { color: '#3b82f6' }
                                                        }
                                                    }}
                                                />
                                            </Grid>

                                            {/* File Upload */}
                                            <Grid size={{ xs: 12 }}>
                                                <Box>
                                                    <Typography
                                                        variant="body2"
                                                        sx={{
                                                            color: '#475569',
                                                            fontWeight: 500,
                                                            mb: 2
                                                        }}
                                                    >
                                                        Attachments (Optional)
                                                    </Typography>
                                                    <Paper
                                                        elevation={0}
                                                        sx={{
                                                            p: 4,
                                                            border: '2px dashed #cbd5e1',
                                                            borderRadius: 2,
                                                            backgroundColor: '#f8fafc',
                                                            textAlign: 'center',
                                                            cursor: 'pointer',
                                                            transition: 'all 0.3s ease',
                                                            '&:hover': {
                                                                borderColor: '#3b82f6',
                                                                backgroundColor: '#f1f5f9',
                                                                transform: 'translateY(-1px)',
                                                            }
                                                        }}
                                                        onClick={() => fileInputRef.current?.click()}
                                                    >
                                                        <input
                                                            type="file"
                                                            hidden
                                                            ref={fileInputRef}
                                                            onChange={handleFileChange}
                                                            multiple
                                                        />
                                                        <CloudUploadIcon
                                                            sx={{
                                                                fontSize: 40,
                                                                color: '#3b82f6',
                                                                mb: 2
                                                            }}
                                                        />
                                                        <Typography variant="h6" sx={{ color: '#1e293b', mb: 1 }}>
                                                            Upload Files
                                                        </Typography>
                                                        <Typography variant="body2" sx={{ color: '#64748b' }}>
                                                            Click to browse or drag and drop your files here
                                                        </Typography>

                                                        {selectedFiles.length > 0 && (
                                                            <Stack direction="row" spacing={1} sx={{ mt: 3, justifyContent: 'center', flexWrap: 'wrap', gap: 1 }}>
                                                                {selectedFiles.map((file, index) => (
                                                                    <Chip
                                                                        key={index}
                                                                        icon={<AttachFileIcon />}
                                                                        label={file.FileName}
                                                                        onDelete={() => removeFile(index)}
                                                                        sx={{
                                                                            backgroundColor: '#3b82f6',
                                                                            color: 'white',
                                                                            fontWeight: 500,
                                                                            '& .MuiChip-deleteIcon': {
                                                                                color: 'rgba(255,255,255,0.8)',
                                                                                '&:hover': {
                                                                                    color: 'white'
                                                                                }
                                                                            }
                                                                        }}
                                                                    />
                                                                ))}
                                                            </Stack>
                                                        )}
                                                    </Paper>
                                                </Box>
                                            </Grid>

                                            {/* Submit Button */}
                                            <Grid size={{ xs: 12 }}>
                                                <Box textAlign="center" mt={4}>
                                                    <Button
                                                        variant="contained"
                                                        type="submit"
                                                        disabled={isDisabled}
                                                        startIcon={<SendIcon />}
                                                        sx={{
                                                            px: 8,
                                                            py: 1.5,
                                                            fontSize: '1rem',
                                                            fontWeight: 600,
                                                            borderRadius: 2,
                                                            backgroundColor: '#3b82f6',
                                                            boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',
                                                            textTransform: 'none',
                                                            transition: 'all 0.3s ease',
                                                            '&:hover': {
                                                                backgroundColor: '#2563eb',
                                                                transform: 'translateY(-2px)',
                                                                boxShadow: '0 8px 25px rgba(59, 130, 246, 0.4)',
                                                            },
                                                            '&:disabled': {
                                                                backgroundColor: '#94a3b8',
                                                                color: 'white',
                                                                boxShadow: 'none'
                                                            }
                                                        }}
                                                    >
                                                        {isDisabled ? 'Submitting...' : 'Submit Feedback'}
                                                    </Button>
                                                </Box>
                                            </Grid>
                                        </Grid>
                                    </form>
                                </CardContent>
                            </Card>
                        </Grow>
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default CreateFeedback;
