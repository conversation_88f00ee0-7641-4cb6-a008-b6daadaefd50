import React from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Typography,
    Grow,
    Stack,
    Chip
} from '@mui/material';
import {
    GetApp as GetAppIcon
} from '@mui/icons-material';
import FeedbackTable from '../FeedbackTable';

// Import SCSS files for styling
import '../../styles/main.scss';

const DataTableCard = ({
    feedbacks = [],
    onExport,
    tableType,
    redirectPage,
    tableTitle = "Ticket Results",
    showExport = true
}) => {
    return (
        <Grow in timeout={1200}>
            <Card
                elevation={0}
                className="data-table-card"
            >
                <CardContent className="table-card-content">
                    {feedbacks && feedbacks.length > 0 && (
                        <Box className="table-header">
                            <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Typography variant="h6" className="table-title">
                                        {tableTitle}
                                    </Typography>
                                    <Chip
                                        label={`${feedbacks?.length || 0} tickets`}
                                        size="small"
                                        className="table-count-chip"
                                    />
                                </Stack>
                                {showExport && (
                                    <Button
                                        variant="outlined"
                                        startIcon={<GetAppIcon />}
                                        onClick={onExport}
                                        className="export-btn"
                                    >
                                        Export Data
                                    </Button>
                                )}
                            </Stack>
                        </Box>
                    )}
                    <Box className="table-content">
                        <FeedbackTable 
                            feedbacks={feedbacks} 
                            type={tableType} 
                            redirectPage={redirectPage} 
                        />
                    </Box>
                </CardContent>
            </Card>
        </Grow>
    );
};

export default DataTableCard;
