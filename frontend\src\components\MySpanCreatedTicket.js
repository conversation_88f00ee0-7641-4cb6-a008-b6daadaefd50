import React, { useState, useEffect } from 'react';
import FeedbackTable from './FeedbackTable';
import { GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetSpanCreatedTickets } from '../services/feedbackService';
// import DatePicker from 'react-datepicker';
// import "react-datepicker/dist/react-datepicker.css";
import '../styles/MyFeedback.css';
import '../styles/FeedbackStats.css';
import alasql from 'alasql';
import * as XLSX from 'xlsx';
import { convertDotNetDate, formatDate } from '../services/CommonHelper';

const MySpanCreatedTicket = () => {
    const [stats, setStats] = useState({
        NEWCASE: 0,
        OPENCASE: 0,
        TATCASE: 0,
        Resolved: 0,
        Closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [spanTicket, setSpanTicket] = useState({});
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(3);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    data.unshift({ Name: "Select", SourceID: 0 });
                    setSource(data);
                    if (userDetails?.EMPData[0]?.ProcessID > 0) {
                        setSelected(prev => ({
                            ...prev,
                            Source: { SourceID: userDetails.EMPData[0].ProcessID }
                        }));
                    }
                }
            })
            .catch(() => {
                setSource([]);
            });
    };

    const GetDashboardCount = (_type) => {
        const objRequest = {
            type: _type,
        };

        GetSpanCreatedTickets(objRequest)
            .then((data) => {
                if (data.length > 0) {
                    setSpanTicket(data);
                    const CategoryCounts = Object.entries(data).map(([category, data]) => ({
                        category: data.Key,
                        ticketCount: data.Value.Count
                    }));
                    if (CategoryCounts && Array.isArray(CategoryCounts) && CategoryCounts.length > 0) {
                        CategoryCounts.forEach(item => {
                            switch (item.category) {
                                case 1:
                                    setStats(prev => ({ ...prev, NEWCASE: item.Count }));
                                    break;
                                case 2:
                                    setStats(prev => ({ ...prev, OPENCASE: item.Count }));
                                    break;
                                case 3:
                                    setStats(prev => ({ ...prev, Resolved: item.Count }));
                                    break;
                                case 4:
                                    setStats(prev => ({ ...prev, Closed: item.Count }));
                                    break;
                                case 5:
                                    setStats(prev => ({ ...prev, TATCASE: item.Count }));
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                } else {
                    setSpanTicket({});
                    setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
                }
            })
            .catch(() => {
                setSpanTicket({});
                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
            });
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    setIssueSubIssue(data);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
            .then((data) => {
                if (data && data.length > 0) {
                    setStatusList(data);
                }
            })
            .catch(() => {
                setStatusList([]);
            });
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;

        var FromDate = formatDateForRequest(fromDate,3);
        var ToDate = formatDateForRequest(toDate,0);

        if(status === 8){
            FromDate = formatDateForRequest(fromDate,0);
            ToDate = formatDateForRequest(toDate,0);
        } 

        FromDate = new Date(FromDate);
        ToDate = new Date(ToDate);

        if (spanTicket != null && spanTicket != {}) {
            var FilteredData = spanTicket;
            var flatdata = Object.values(FilteredData).flatMap(group => group.Value.Tickets);
            if (flatdata && Array.isArray(flatdata) && flatdata.length > 0)
            {
                FilteredData = Array.from(
                    new Map(flatdata.map(item => [item.TicketDisplayID, item])).values()
                );

                //filter based on fromdate to date
                FilteredData = FilteredData.filter(ticket => {
                    const createdOn = new Date(convertDotNetDate(ticket.CreatedOn));
                    return createdOn >= FromDate && createdOn <= ToDate;
                });

                //Selected Status
                if (statusId > 0) {
                    FilteredData = spanTicket[(statusId - 1).toString()]?.Value?.Tickets || [];
                }

                //Selected Process
                if (selected && selected.Source && selected.Source.SourceID > 0) {
                    FilteredData = FilteredData.filter(ticket => {
                        const ProcessName = selected.Source.Name;
                        return ProcessName == ticket.Process;
                    });
                }

                //Selected Sub-Process
                if (selected && selected.IssueType && selected.IssueType.IssueID > 0) {
                    FilteredData = FilteredData.filter(ticket => {
                        const IssuName = selected.IssueType.ISSUENAME;
                        return IssuName == ticket.IssueStatus;
                    });
                }

                //Selected ProductID
                if (selected && selected.Product && selected.Product.ProductID > 0) {
                    FilteredData = FilteredData.filter(ticket => {
                        return selected.Product.ProductID == ticket.ProductId;
                    });
                }
                //Selected TicketID
                if (ticketId != undefined && ticketId.trim() != '') {
                    FilteredData = FilteredData.filter(ticket => {
                        return ticketId.trim().toUpperCase() == ticket.TicketDisplayID.toUpperCase();
                    });
                }
            }
            setFeedbacks(FilteredData);
        }

    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const exportData = () => {
        if (typeof window !== 'undefined') {
            window.XLSX = XLSX;
        }

        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';
            
            return formatDate(dateStr);
        };
        
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,CreatedByUserName as Name,'
            + 'CreatedByEmployeeId as EmpID,'
            + 'AssignToUserName as AssignTo,AssignToEmployeeID as AssignToEcode,'
            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'
            + ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ',
            [feedbacks]
        );
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE || 0, id: 1,  className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE || 0, id: 2,  className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5,  className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved || 0, id: 3,  className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed || 0, id: 4,  className: 'closed-status' }
    ];

    return (
        <div className="container-fluid">
            <div className="block-header">
                <div className="row">
                    <div className="col-lg-6 col-md-8 col-lg-12">
                        <ul className="breadcrumb adv_search">
                            <li className="breadcrumb-item active"><b>My Span</b></li>
                        </ul>
                        <div className="col-lg-6 hidden-sm text-right switch_btns">
                            <button className="btn btn-sm btn-outline-info" onClick={() => setActiveSearchType(1)}>Search</button>
                            <button 
                                className="btn btn-sm btn-outline-secondary" 
                                onClick={() => {
                                    setActiveSearchType(2)
                                    setSelected({
                                        Source: { SourceID: 0, Name: 'Select' },
                                        IssueType: undefined,
                                        Status: undefined
                                    });
                                    setTicketId('');
                                }} 
                            >Dashboard</button>
                        </div>
                    </div>
                </div>
            </div>

            {activeSearchType === 2 && (
                <div className="feedback-stats">
                    {statCards.map((stat) => (
                        <div
                            key={stat.label}
                            className={`stat-card ${stat.className}`}
                            style={{ backgroundColor: stat.color }}
                            onClick={() => GetAgentTicketList(stat.id)}
                        >
                            <h2>{stat.count}</h2>
                            <p>{stat.label}</p>
                        </div>
                    ))}
                </div>
            )}

            {activeSearchType === 1 && (
                <div className="row clearfix">
                    <div className="col-md-12">
                        <div className="card">
                            <div className="body">
                                <div className="row clearfix">
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>From</label>
                                            {/* <DatePicker
                                                selected={fromDate}
                                                onChange={date => setFromDate(date)}
                                                className="form-control"
                                                dateFormat="dd-MM-yyyy"
                                            /> */}
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>To</label>
                                            {/* <DatePicker
                                                selected={toDate}
                                                onChange={date => setToDate(date)}
                                                className="form-control"
                                                dateFormat="dd-MM-yyyy"
                                            /> */}
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Process</label>
                                            <select 
                                                className="form-control"
                                                value={selected.Source?.SourceID || 0}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    Source: { SourceID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                {source.map(s => (
                                                    <option key={s.SourceID} value={s.SourceID}>{s.Name}</option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                    {selected.Source?.SourceID && [2, 4, 5, 8].includes(selected.Source?.SourceID) && (
                                        <div className="col-lg-3 col-md-6 col-sm-12">
                                            <div className="form-group">
                                                <label>Product</label>
                                                <select 
                                                    className="form-control"
                                                    value={selected.Source?.SourceID || 0}
                                                    onChange={(e) => setSelected(prev => ({
                                                        ...prev,
                                                        Product: parseInt(e.target.value)
                                                    }))}
                                                >
                                                    {ProductOptions.map(p => (
                                                        <option key={p.ProductID} value={p.ProductID}>{p.Name}</option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>
                                    )}
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Feedback</label>
                                            <select
                                                className="form-control"
                                                value={selected.IssueType?.IssueID || ''}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    IssueType: { IssueID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                <option value="">Select Feedback</option>
                                                {issueSubIssue
                                                    .filter(item => item.SourceID === selected.Source?.SourceID)
                                                    .map(issue => (
                                                        <option key={issue.IssueID} value={issue.IssueID}>
                                                            {issue.ISSUENAME}
                                                        </option>
                                                    ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Status</label>
                                            <select
                                                className="form-control"
                                                value={selected.Status?.StatusID || ''}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    Status: { StatusID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                <option value="">Select Status</option>
                                                {statusList.map(status => (
                                                    <option key={status.StatusID} value={status.StatusID}>
                                                        {status.StatusName}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="form-group">
                                            <label>Ticket ID</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                value={ticketId}
                                                onChange={(e) => setTicketId(e.target.value)}
                                            />
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-12">
                                        <div className="m-t-15 advance_search_btn">
                                            <button className="btn btn-primary" onClick={() => GetAgentTicketList(8)}>
                                                Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="row clearfix">
                <div className="col-md-12">
                    {feedbacks.length > 0 && (
                        <button className="btn btn-info" onClick={exportData}>Export Data</button>
                    )}
                    <div className="card">
                        <div className="body">
                            <FeedbackTable feedbacks={feedbacks} type={4} redirectPage='/TicketDetails/'/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MySpanCreatedTicket;