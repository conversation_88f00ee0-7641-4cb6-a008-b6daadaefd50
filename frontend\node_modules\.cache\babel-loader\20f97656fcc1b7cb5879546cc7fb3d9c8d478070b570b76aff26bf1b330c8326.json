{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\common\\\\DataTableCard.js\";\nimport React from 'react';\nimport { Box, But<PERSON>, Card, CardContent, Typography, Grow, Stack, Chip } from '@mui/material';\nimport { GetApp as GetAppIcon } from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataTableCard = ({\n  feedbacks,\n  onExport,\n  tableType,\n  redirectPage,\n  tableTitle = \"Ticket Results\",\n  showExport = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(Grow, {\n    in: true,\n    timeout: 1200,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      className: \"data-table-card\",\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        className: \"table-card-content\",\n        children: [feedbacks.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          className: \"table-header\",\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                className: \"table-title\",\n                children: tableTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `${feedbacks.length} tickets`,\n                size: \"small\",\n                className: \"table-count-chip\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 33\n            }, this), showExport && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(GetAppIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 52\n              }, this),\n              onClick: onExport,\n              className: \"export-btn\",\n              children: \"Export Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"table-content\",\n          children: /*#__PURE__*/_jsxDEV(FeedbackTable, {\n            feedbacks: feedbacks,\n            type: tableType,\n            redirectPage: redirectPage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 9\n  }, this);\n};\n_c = DataTableCard;\nexport default DataTableCard;\nvar _c;\n$RefreshReg$(_c, \"DataTableCard\");", "map": {"version": 3, "names": ["React", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grow", "<PERSON><PERSON>", "Chip", "GetApp", "GetAppIcon", "FeedbackTable", "jsxDEV", "_jsxDEV", "DataTableCard", "feedbacks", "onExport", "tableType", "redirectPage", "tableTitle", "showExport", "in", "timeout", "children", "elevation", "className", "length", "direction", "spacing", "alignItems", "justifyContent", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "size", "startIcon", "onClick", "type", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/common/DataTableCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n    <PERSON>,\n    <PERSON><PERSON>,\n    Card,\n    CardContent,\n    Typography,\n    Grow,\n    Stack,\n    Chip\n} from '@mui/material';\nimport {\n    GetApp as GetAppIcon\n} from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\nconst DataTableCard = ({\n    feedbacks,\n    onExport,\n    tableType,\n    redirectPage,\n    tableTitle = \"Ticket Results\",\n    showExport = true\n}) => {\n    return (\n        <Grow in timeout={1200}>\n            <Card\n                elevation={0}\n                className=\"data-table-card\"\n            >\n                <CardContent className=\"table-card-content\">\n                    {feedbacks.length > 0 && (\n                        <Box className=\"table-header\">\n                            <Stack direction=\"row\" spacing={2} alignItems=\"center\" justifyContent=\"space-between\">\n                                <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n                                    <Typography variant=\"h6\" className=\"table-title\">\n                                        {tableTitle}\n                                    </Typography>\n                                    <Chip\n                                        label={`${feedbacks.length} tickets`}\n                                        size=\"small\"\n                                        className=\"table-count-chip\"\n                                    />\n                                </Stack>\n                                {showExport && (\n                                    <Button\n                                        variant=\"outlined\"\n                                        startIcon={<GetAppIcon />}\n                                        onClick={onExport}\n                                        className=\"export-btn\"\n                                    >\n                                        Export Data\n                                    </Button>\n                                )}\n                            </Stack>\n                        </Box>\n                    )}\n                    <Box className=\"table-content\">\n                        <FeedbackTable \n                            feedbacks={feedbacks} \n                            type={tableType} \n                            redirectPage={redirectPage} \n                        />\n                    </Box>\n                </CardContent>\n            </Card>\n        </Grow>\n    );\n};\n\nexport default DataTableCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,QACD,eAAe;AACtB,SACIC,MAAM,IAAIC,UAAU,QACjB,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,aAAa,GAAGA,CAAC;EACnBC,SAAS;EACTC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG,gBAAgB;EAC7BC,UAAU,GAAG;AACjB,CAAC,KAAK;EACF,oBACIP,OAAA,CAACP,IAAI;IAACe,EAAE;IAACC,OAAO,EAAE,IAAK;IAAAC,QAAA,eACnBV,OAAA,CAACV,IAAI;MACDqB,SAAS,EAAE,CAAE;MACbC,SAAS,EAAC,iBAAiB;MAAAF,QAAA,eAE3BV,OAAA,CAACT,WAAW;QAACqB,SAAS,EAAC,oBAAoB;QAAAF,QAAA,GACtCR,SAAS,CAACW,MAAM,GAAG,CAAC,iBACjBb,OAAA,CAACZ,GAAG;UAACwB,SAAS,EAAC,cAAc;UAAAF,QAAA,eACzBV,OAAA,CAACN,KAAK;YAACoB,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACC,UAAU,EAAC,QAAQ;YAACC,cAAc,EAAC,eAAe;YAAAP,QAAA,gBACjFV,OAAA,CAACN,KAAK;cAACoB,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAACC,UAAU,EAAC,QAAQ;cAAAN,QAAA,gBAClDV,OAAA,CAACR,UAAU;gBAAC0B,OAAO,EAAC,IAAI;gBAACN,SAAS,EAAC,aAAa;gBAAAF,QAAA,EAC3CJ;cAAU;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbtB,OAAA,CAACL,IAAI;gBACD4B,KAAK,EAAE,GAAGrB,SAAS,CAACW,MAAM,UAAW;gBACrCW,IAAI,EAAC,OAAO;gBACZZ,SAAS,EAAC;cAAkB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACPf,UAAU,iBACPP,OAAA,CAACX,MAAM;cACH6B,OAAO,EAAC,UAAU;cAClBO,SAAS,eAAEzB,OAAA,CAACH,UAAU;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BI,OAAO,EAAEvB,QAAS;cAClBS,SAAS,EAAC,YAAY;cAAAF,QAAA,EACzB;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACR,eACDtB,OAAA,CAACZ,GAAG;UAACwB,SAAS,EAAC,eAAe;UAAAF,QAAA,eAC1BV,OAAA,CAACF,aAAa;YACVI,SAAS,EAAEA,SAAU;YACrByB,IAAI,EAAEvB,SAAU;YAChBC,YAAY,EAAEA;UAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAACM,EAAA,GApDI3B,aAAa;AAsDnB,eAAeA,aAAa;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}