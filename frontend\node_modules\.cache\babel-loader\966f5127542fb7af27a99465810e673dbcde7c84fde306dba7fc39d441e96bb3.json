{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\n// Import Bootstrap and its dependencies\nimport 'jquery/dist/jquery.min.js';\nimport 'popper.js/dist/umd/popper.min.js';\n// import 'bootstrap/dist/js/bootstrap.min.js';\n// import 'bootstrap/dist/css/bootstrap.min.css';\n\n// Import Font Awesome\nimport '@fortawesome/fontawesome-free/css/all.min.css';\n\n// Import Google Fonts\nimport 'typeface-open-sans';\n\n// Import custom SCSS styles\nimport './styles/main.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 3\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 22,\n  columnNumber: 2\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport App from './App';\r\n\r\n// Import Bootstrap and its dependencies\r\nimport 'jquery/dist/jquery.min.js';\r\nimport 'popper.js/dist/umd/popper.min.js';\r\n// import 'bootstrap/dist/js/bootstrap.min.js';\r\n// import 'bootstrap/dist/css/bootstrap.min.css';\r\n\r\n// Import Font Awesome\r\nimport '@fortawesome/fontawesome-free/css/all.min.css';\r\n\r\n// Import Google Fonts\r\nimport 'typeface-open-sans';\r\n\r\n// Import custom SCSS styles\r\nimport './styles/main.scss';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\nroot.render(\r\n\t<React.StrictMode>\r\n\t\t<App />\r\n\t</React.StrictMode>\r\n);\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AACA,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC;AACA;;AAEA;AACA,OAAO,+CAA+C;;AAEtD;AACA,OAAO,oBAAoB;;AAE3B;AACA,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACVL,OAAA,CAACJ,KAAK,CAACU,UAAU;EAAAC,QAAA,eAChBP,OAAA,CAACF,GAAG;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACU,CACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}