import React, { useState, useEffect } from 'react';
import FeedbackTable from './FeedbackTable';
import {
    GetSalesTicketCount,
    GetProcessMasterByAPI,
    GetAllIssueSubIssue,
    getStatusMaster,
    GetAdminTicketList
} from '../services/feedbackService';
import '../styles/MyFeedback.css';
import '../styles/FeedbackStats.css';
import * as XLSX from 'xlsx';
import alasql from 'alasql';
import { formatDate } from '../services/CommonHelper';

import {
    Box,
    Card,
    CardContent,
    Grid2 as Grid,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    IconButton,
    Tooltip,
    Typography,
    Button,
    Chip,
    Stack,
    Container,
    Grow,
    Fade,
    Paper
} from '@mui/material';

import RefreshIcon from '@mui/icons-material/Refresh';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import GetAppIcon from '@mui/icons-material/GetApp';
import DashboardIcon from '@mui/icons-material/Dashboard';

const MySpanTickets = () => {
    const [stats, setStats] = useState({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(3);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI().then((data) => {
            if (data && data.length > 0) {
                data.unshift({ Name: "Select", SourceID: 0 });
                setSource(data);
                if (userDetails?.EMPData[0]?.ProcessID > 0) {
                    setSelected(prev => ({
                        ...prev,
                        Source: { SourceID: userDetails.EMPData[0].ProcessID }
                    }));
                }
            } else setSource([]);
        }).catch(() => setSource([]));
    };

    const GetDashboardCount = (_type) => {
        GetSalesTicketCount({ type: _type }).then((data) => {
            const newStats = { NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 };
            data?.forEach(item => {
                switch (item.StatusID) {
                    case 1: newStats.NEWCASE = item.Count; break;
                    case 2: newStats.OPENCASE = item.Count; break;
                    case 3: newStats.Resolved = item.Count; break;
                    case 4: newStats.Closed = item.Count; break;
                    case 5: newStats.TATCASE = item.Count; break;
                    default: break;
                }
            });
            setStats(newStats);
        }).catch(() => setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 }));
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue().then(data => setIssueSubIssue(data || [])).catch(() => setIssueSubIssue([]));
    };

    const getAllStatusMaster = () => {
        getStatusMaster().then(data => setStatusList(data || [])).catch(() => setStatusList([]));
    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;
        let fromDateStr = formatDateForRequest(fromDate, 3);
        let toDateStr = formatDateForRequest(toDate, 0);
        if (status === 8) {
            fromDateStr = formatDateForRequest(fromDate, 0);
            toDateStr = formatDateForRequest(toDate, 0);
        }
        const obj = {
            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,
            FromDate: fromDateStr,
            ToDate: toDateStr,
            ProcessID: userDetails?.EMPData[0]?.ProcessID ?? 0,
            IssueID: selected.IssueType?.IssueID || 0,
            StatusID: statusId,
            TicketID: 0,
            TicketDisplayID: ticketId?.trim() || "",
            ProductID: selected.Product?.ProductID || 0
        };
        GetAdminTicketList(obj).then(data => {
            const sorted = data?.length ? [...data].sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn)) : [];
            setFeedbacks(sorted);
        }).catch(() => setFeedbacks([]));
    };

    const exportData = () => {
        if (typeof window !== 'undefined') window.XLSX = XLSX;
        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';
            return formatDate(dateStr);
        };
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,' +
            'CreatedByDetails -> EmployeeID as EmpID,' +
            'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,' +
            'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn' +
            ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ',
            [feedbacks]
        );
    };

    const resetFilters = () => {
        setFromDate(new Date());
        setToDate(new Date());
        setSelected({
            Source: { SourceID: 0, Name: 'Select' },
            IssueType: undefined,
            Status: undefined,
            Product: { ProductID: 0, Name: 'Select' }
        });
        setTicketId('');
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE, id: 1, className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE, id: 2, className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE, id: 5, className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved, id: 3, className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed, id: 4, className: 'closed-status' }
    ];

    return (
        <Box className="assigned-tickets-main">
            <Container maxWidth="xl" className="assigned-tickets-container">
                <Fade in timeout={800}>
                    <Box>
                     

                       {/* Header Section */}
                        <Paper elevation={0} className="tickets-header">
                            <Box className="header-decoration-1" />
                            <Box className="header-decoration-2" />

                            <Grid container spacing={2} alignItems="center" className="header-content">
                                <Grid size={{ xs: 12, md: 8 }}>
                                    <Stack direction="row" spacing={2} alignItems="center">
                                        {/* <AssignmentIcon className="header-icon" /> */}
                                        <Box>
                                            <Typography variant="h4" className="header-title">
                                             My Span Tickets
                                            </Typography>
                                            <Typography variant="body1" className="header-subtitle">
                                              Track Span Feedback Tickets
                                            </Typography>
                                        </Box>
                                    </Stack>
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <Stack direction="row" spacing={2} justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                                        <Button
                                            variant={activeSearchType === 2 ? "contained" : "outlined"}
                                            startIcon={<DashboardIcon />}
                                            onClick={() => {
                                                setActiveSearchType(2);
                                                resetFilters();
                                            }}
                                            className={`header-btn ${activeSearchType === 2 ? 'active' : ''}`}
                                        >
                                            Dashboard
                                        </Button>
                                        <Button
                                            variant={activeSearchType === 1 ? "contained" : "outlined"}
                                            startIcon={<SearchIcon />}
                                            onClick={() => setActiveSearchType(1)}
                                            className={`header-btn ${activeSearchType === 1 ? 'active' : ''}`}
                                        >
                                            Search
                                        </Button>
                                    </Stack>
                                </Grid>
                            </Grid>
                        </Paper>

                         {activeSearchType === 2 && (
                         
                               <div className="feedback-stats">
                               {statCards.map((stat) => (
                                   <div
                                       key={stat.label}
                                       className={`stat-card ${stat.className}`}                                       
                                       onClick={() => GetAgentTicketList(stat.id)}
                                   >
                                       <h2>{stat.count}</h2>
                                       <p>{stat.label}</p>
                                   </div>
                               ))}
                           </div>
                        )}
                        {/* Search Form */}
                        {activeSearchType === 1 && (
                            <Grow in timeout={1000}>
                                <Card
                                    elevation={0}
                                    className="search-form-card"
                                >
                                    <CardContent className="search-form-content">
                                        <Stack direction="row" spacing={2} alignItems="center" className="search-form-header">
                                            <FilterListIcon className="filter-icon" />
                                            <Typography variant="h6" className="search-form-title">
                                                Advanced Search Filters
                                            </Typography>
                                        </Stack>

                                        <Grid container spacing={3}>
                                            {/* Date Range */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <TextField
                                                    label="From Date"
                                                    type="date"
                                                    fullWidth
                                                    value={fromDate.toISOString().split('T')[0]}
                                                    onChange={(e) => setFromDate(new Date(e.target.value))}
                                                    InputLabelProps={{ shrink: true }}
                                                    className="form-field"
                                                />
                                            </Grid>

                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <TextField
                                                    label="To Date"
                                                    type="date"
                                                    fullWidth
                                                    value={toDate.toISOString().split('T')[0]}
                                                    onChange={(e) => setToDate(new Date(e.target.value))}
                                                    InputLabelProps={{ shrink: true }}
                                                    className="form-field"
                                                />
                                            </Grid>

                                            {/* Process */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth className="form-field">
                                                    <InputLabel>Process</InputLabel>
                                                    <Select
                                                        label="Process"
                                                        value={selected.Source?.SourceID || 0}
                                                        onChange={(e) => setSelected(prev => ({
                                                            ...prev,
                                                            Source: { SourceID: parseInt(e.target.value) }
                                                        }))}
                                                    >
                                                        {source.map(s => (
                                                            <MenuItem key={s.SourceID} value={s.SourceID}>{s.Name}</MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Feedback Type */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth className="form-field">
                                                    <InputLabel>Feedback Type</InputLabel>
                                                    <Select
                                                        label="Feedback Type"
                                                        value={selected.IssueType?.IssueID || ''}
                                                        onChange={(e) => setSelected(prev => ({
                                                            ...prev,
                                                            IssueType: { IssueID: parseInt(e.target.value) }
                                                        }))}
                                                    >
                                                        <MenuItem value="">Select Feedback</MenuItem>
                                                        {issueSubIssue
                                                            .filter(item => item.SourceID === selected.Source?.SourceID)
                                                            .map(issue => (
                                                                <MenuItem key={issue.IssueID} value={issue.IssueID}>
                                                                    {issue.ISSUENAME}
                                                                </MenuItem>
                                                            ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Status */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth className="form-field">
                                                    <InputLabel>Status</InputLabel>
                                                    <Select
                                                        label="Status"
                                                        value={selected.Status?.StatusID || ''}
                                                        onChange={(e) => setSelected(prev => ({
                                                            ...prev,
                                                            Status: { StatusID: parseInt(e.target.value) }
                                                        }))}
                                                    >
                                                        <MenuItem value="">Select Status</MenuItem>
                                                        {statusList.map(status => (
                                                            <MenuItem key={status.StatusID} value={status.StatusID}>
                                                                {status.StatusName}
                                                            </MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            {/* Feedback ID */}
                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <TextField
                                                    label="Feedback ID"
                                                    fullWidth
                                                    value={ticketId}
                                                    onChange={(e) => setTicketId(e.target.value)}
                                                    placeholder="Enter Feedback ID"
                                                    className="form-field"
                                                />
                                            </Grid>

                                            {/* Action Buttons */}
                                            <Grid size={{ xs: 12, md: 6 }}>
                                                <Stack direction="row" spacing={2} >
                                                    <Button
                                                        variant="contained"
                                                        startIcon={<SearchIcon />}
                                                        onClick={() => GetAgentTicketList(8)}
                                                        className="search-btn"
                                                    >
                                                        Search Tickets
                                                    </Button>
                                                    <Button
                                                        variant="outlined"
                                                        startIcon={<RefreshIcon />}
                                                        onClick={resetFilters}
                                                        className="reset-btn"
                                                    >
                                                        Reset Filters
                                                    </Button>
                                                </Stack>
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grow>
                        )}

                        <Grow in timeout={1200}>
                            <Card elevation={0} className="data-table-card">
                                <CardContent>
                                    {feedbacks.length > 0 && (
                                        <Box className="table-header">
                                            <Stack direction="row" spacing={2} justifyContent="space-between">
                                                <Stack direction="row" spacing={2} alignItems="center">
                                                    <Typography variant="h6">Process Ticket Results</Typography>
                                                    <Chip label={`${feedbacks.length} tickets`} size="small" />
                                                </Stack>
                                                <Button variant="outlined" startIcon={<GetAppIcon />} onClick={exportData}>Export Data</Button>
                                            </Stack>
                                        </Box>
                                    )}
                                    <Box className="table-content">
                                        <FeedbackTable feedbacks={feedbacks} type={3} redirectPage='/TicketDetails/' />
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grow>
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default MySpanTickets;
