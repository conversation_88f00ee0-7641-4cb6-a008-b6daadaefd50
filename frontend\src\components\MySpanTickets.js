import React, { useState, useEffect } from 'react';
import {
    GetSalesTicketCount,
    GetProcessMasterByAPI,
    GetAllIssueSubIssue,
    getStatusMaster,
    GetAdminTicketList
} from '../services/feedbackService';
import '../styles/MyFeedback.css';
import '../styles/FeedbackStats.css';
import '../styles/MyAssignedTickets.css';
import * as XLSX from 'xlsx';
import alasql from 'alasql';
import { formatDate } from '../services/CommonHelper';

// Common Components
import TicketPageHeader from './common/TicketPageHeader';
import DashboardStats from './common/DashboardStats';
import DataTableCard from './common/DataTableCard';

import {
    Box,
    Container,
    Fade
} from '@mui/material';
import {
    SupervisorAccount as SupervisorAccountIcon
} from '@mui/icons-material';

const MySpanTickets = () => {
    const [stats, setStats] = useState({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(3);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI().then((data) => {
            if (data && data.length > 0) {
                data.unshift({ Name: "Select", SourceID: 0 });
                setSource(data);
                if (userDetails?.EMPData[0]?.ProcessID > 0) {
                    setSelected(prev => ({
                        ...prev,
                        Source: { SourceID: userDetails.EMPData[0].ProcessID }
                    }));
                }
            } else setSource([]);
        }).catch(() => setSource([]));
    };

    const GetDashboardCount = (_type) => {
        GetSalesTicketCount({ type: _type }).then((data) => {
            const newStats = { NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 };
            data?.forEach(item => {
                switch (item.StatusID) {
                    case 1: newStats.NEWCASE = item.Count; break;
                    case 2: newStats.OPENCASE = item.Count; break;
                    case 3: newStats.Resolved = item.Count; break;
                    case 4: newStats.Closed = item.Count; break;
                    case 5: newStats.TATCASE = item.Count; break;
                    default: break;
                }
            });
            setStats(newStats);
        }).catch(() => setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 }));
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue().then(data => setIssueSubIssue(data || [])).catch(() => setIssueSubIssue([]));
    };

    const getAllStatusMaster = () => {
        getStatusMaster().then(data => setStatusList(data || [])).catch(() => setStatusList([]));
    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;
        let fromDateStr = formatDateForRequest(fromDate, 3);
        let toDateStr = formatDateForRequest(toDate, 0);
        if (status === 8) {
            fromDateStr = formatDateForRequest(fromDate, 0);
            toDateStr = formatDateForRequest(toDate, 0);
        }
        const obj = {
            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,
            FromDate: fromDateStr,
            ToDate: toDateStr,
            ProcessID: userDetails?.EMPData[0]?.ProcessID ?? 0,
            IssueID: selected.IssueType?.IssueID || 0,
            StatusID: statusId,
            TicketID: 0,
            TicketDisplayID: ticketId?.trim() || "",
            ProductID: selected.Product?.ProductID || 0
        };
        GetAdminTicketList(obj).then(data => {
            const sorted = data?.length ? [...data].sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn)) : [];
            setFeedbacks(sorted);
        }).catch(() => setFeedbacks([]));
    };

    const exportData = () => {
        if (typeof window !== 'undefined') window.XLSX = XLSX;
        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';
            return formatDate(dateStr);
        };
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,' +
            'CreatedByDetails -> EmployeeID as EmpID,' +
            'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,' +
            'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn' +
            ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ',
            [feedbacks]
        );
    };

    const resetFilters = () => {
        setFromDate(new Date());
        setToDate(new Date());
        setSelected({
            Source: { SourceID: 0, Name: 'Select' },
            IssueType: undefined,
            Status: undefined,
            Product: { ProductID: 0, Name: 'Select' }
        });
        setTicketId('');
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE, id: 1, className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE, id: 2, className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE, id: 5, className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved, id: 3, className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed, id: 4, className: 'closed-status' }
    ];

    return (
        <Box className="page-container">
            <Container maxWidth="xl" className="main-container">
                <Fade in timeout={800}>
                    <Box>
                        {/* Header and Search Form */}
                        <TicketPageHeader
                            title="My Process Tickets"
                            subtitle="Manage tickets within your process area"
                            icon={SupervisorAccountIcon}
                            activeSearchType={activeSearchType}
                            setActiveSearchType={setActiveSearchType}
                            resetFilters={resetFilters}
                            fromDate={fromDate}
                            setFromDate={setFromDate}
                            toDate={toDate}
                            setToDate={setToDate}
                            selected={selected}
                            setSelected={setSelected}
                            ticketId={ticketId}
                            setTicketId={setTicketId}
                            source={source}
                            issueSubIssue={issueSubIssue}
                            statusList={statusList}
                            ProductOptions={ProductOptions}
                            onSearch={() => GetAgentTicketList(8)}
                            showProductField={true}
                            searchButtonText="Search Tickets"
                        />

                        {/* Dashboard Stats */}
                        {activeSearchType === 2 && (
                            <DashboardStats
                                statCards={statCards}
                                onStatClick={GetAgentTicketList}
                            />
                        )}


                        {/* Data Table */}
                        <DataTableCard
                            feedbacks={feedbacks}
                            onExport={exportData}
                            tableType={3}
                            redirectPage="/TicketDetails/"
                            tableTitle="Process Ticket Results"
                        />
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default MySpanTickets;
