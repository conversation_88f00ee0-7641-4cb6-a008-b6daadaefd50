{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\common\\\\DashboardStats.js\";\nimport React from 'react';\nimport '../../../styles/FeedbackStats.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardStats = ({\n  statCards,\n  onStatClick,\n  className = \"feedback-stats\"\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    children: statCards.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `stat-card ${stat.className}`,\n      style: {\n        backgroundColor: stat.color\n      },\n      onClick: () => onStatClick(stat.id || stat.Id),\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: stat.count\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: stat.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 21\n      }, this)]\n    }, stat.label, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_c = DashboardStats;\nexport default DashboardStats;\nvar _c;\n$RefreshReg$(_c, \"DashboardStats\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DashboardStats", "statCards", "onStatClick", "className", "children", "map", "stat", "style", "backgroundColor", "color", "onClick", "id", "Id", "count", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/common/DashboardStats.js"], "sourcesContent": ["import React from 'react';\nimport '../../../styles/FeedbackStats.css';\n\nconst DashboardStats = ({ \n    statCards, \n    onStatClick,\n    className = \"feedback-stats\" \n}) => {\n    return (\n        <div className={className}>\n            {statCards.map((stat) => (\n                <div\n                    key={stat.label}\n                    className={`stat-card ${stat.className}`}\n                    style={{ backgroundColor: stat.color }}\n                    onClick={() => onStatClick(stat.id || stat.Id)}\n                >\n                    <h2>{stat.count}</h2>\n                    <p>{stat.label}</p>\n                </div>\n            ))}\n        </div>\n    );\n};\n\nexport default DashboardStats;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,cAAc,GAAGA,CAAC;EACpBC,SAAS;EACTC,WAAW;EACXC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF,oBACIJ,OAAA;IAAKI,SAAS,EAAEA,SAAU;IAAAC,QAAA,EACrBH,SAAS,CAACI,GAAG,CAAEC,IAAI,iBAChBP,OAAA;MAEII,SAAS,EAAE,aAAaG,IAAI,CAACH,SAAS,EAAG;MACzCI,KAAK,EAAE;QAAEC,eAAe,EAAEF,IAAI,CAACG;MAAM,CAAE;MACvCC,OAAO,EAAEA,CAAA,KAAMR,WAAW,CAACI,IAAI,CAACK,EAAE,IAAIL,IAAI,CAACM,EAAE,CAAE;MAAAR,QAAA,gBAE/CL,OAAA;QAAAK,QAAA,EAAKE,IAAI,CAACO;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrBlB,OAAA;QAAAK,QAAA,EAAIE,IAAI,CAACY;MAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA,GANdX,IAAI,CAACY,KAAK;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOd,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACE,EAAA,GApBInB,cAAc;AAsBpB,eAAeA,cAAc;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}