/* Data Table and Grid Styles - SCSS Format */

// Import variables from layout
@import './layout.scss';

// Data Table Card
.data-table-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
}

.table-card-content {
  padding: 0 !important;
}

.table-header {
  padding: 1.5rem !important;
  border-bottom: 1px solid $border-color;
}

.table-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

.table-count-chip {
  background-color: #e0f2fe !important;
  color: #0277bd !important;
  font-weight: 600 !important;
}

.export-btn {
  border-radius: 0.5rem !important;
  border-color: $success-color !important;
  color: $success-color !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    background-color: $success-color !important;
    color: $white !important;
    transform: translateY(-1px);
  }
}

.table-content {
  padding: 1.5rem !important;
}

// Dashboard Stats (Feedback Stats)
.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem 0;

  .stat-card {
    background: $white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    @include card-shadow;
    @include transition-smooth;
    cursor: pointer;
    border: 1px solid $border-color;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, transparent, rgba($white, 0.8), transparent);
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: $white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: $white;
      opacity: 0.9;
    }

    // Status-specific styling
    &.new-status {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    &.open-status {
      background: linear-gradient(135deg, #fcb69f 0%, #ffecd2 100%);
    }

    &.tat-status {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }

    &.resolved-status {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    &.closed-status {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
}

// Table Responsive Design
@media (max-width: 768px) {
  .table-header {
    padding: 1rem !important;
    
    .MuiStack-root {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start !important;
    }
  }

  .table-content {
    padding: 1rem !important;
  }

  .feedback-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;

    .stat-card {
      padding: 1rem;

      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .feedback-stats {
    grid-template-columns: 1fr 1fr;
    
    .stat-card {
      padding: 0.75rem;

      h2 {
        font-size: 1.5rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .table-title {
    font-size: 1.1rem !important;
  }

  .export-btn {
    font-size: 0.8rem !important;
    padding: 0.5rem 1rem !important;
  }
}

// Table Loading State
.table-loading {
  @include flex-center;
  padding: 3rem;
  color: $text-secondary;
  
  .loading-spinner {
    margin-right: 1rem;
  }
}

// Empty State
.table-empty {
  @include flex-center;
  flex-direction: column;
  padding: 3rem;
  color: $text-secondary;
  
  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }
  
  .empty-message {
    font-size: 1.1rem;
    font-weight: 500;
  }
}

// Table Pagination
.table-pagination {
  padding: 1rem 1.5rem;
  border-top: 1px solid $border-color;
  background-color: $background-color;
  border-radius: 0 0 1rem 1rem;
}

// MyFeedback specific styles
.my-feedback {
  padding: 0;
}

.loading {
  @include flex-center;
  min-height: 200px;
  font-size: 14px;
  color: $text-secondary;
}
