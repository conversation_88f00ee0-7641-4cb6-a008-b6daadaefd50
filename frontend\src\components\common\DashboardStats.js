import React from 'react';
import '../../../styles/FeedbackStats.css';

const DashboardStats = ({ 
    statCards, 
    onStatClick,
    className = "feedback-stats" 
}) => {
    return (
        <div className={className}>
            {statCards.map((stat) => (
                <div
                    key={stat.label}
                    className={`stat-card ${stat.className}`}
                    style={{ backgroundColor: stat.color }}
                    onClick={() => onStatClick(stat.id || stat.Id)}
                >
                    <h2>{stat.count}</h2>
                    <p>{stat.label}</p>
                </div>
            ))}
        </div>
    );
};

export default DashboardStats;
