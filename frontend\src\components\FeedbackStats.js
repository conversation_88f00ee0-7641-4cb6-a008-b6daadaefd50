import React from 'react';
import '../styles/scss/main.scss';

const FeedbackStats = ({ stats, GetAllTicketList }) => {
	const statCards = [
		{ label: 'New', count: stats.new || 0, Id: 1, color: '#4facfe', className: 'new-status' },
		{ label: 'Open', count: stats.open || 0, Id: 2, color: '#fcb69f', className: 'open-status' },
		{ label: 'Resolved', count: stats.resolved || 0 , Id: 3, color: '#a8edea', className: 'resolved-status' },
		{ label: 'Closed', count: stats.closed || 0, Id: 4, color: '#2c3e50', className: 'closed-status' }
	];

	return (
		<div className="feedback-stats">
			{statCards.map((stat) => (
				<div
					key={stat.label}
					className={`stat-card ${stat.className}`}
					style={{ backgroundColor: stat.color }}
					onClick={() => GetAllTicketList(stat.Id)}
				>
					<h2>{stat.count}</h2>
					<p>{stat.label}</p>
				</div>
			))}
		</div>
	);
};

export default FeedbackStats; 