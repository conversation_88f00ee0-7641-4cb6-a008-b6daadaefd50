{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\MySpanTickets.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { GetSalesTicketCount, GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetAdminTicketList } from '../services/feedbackService';\nimport '../styles/MyFeedback.css';\nimport '../styles/FeedbackStats.css';\nimport '../styles/MyAssignedTickets.css';\nimport * as XLSX from 'xlsx';\nimport alasql from 'alasql';\nimport { formatDate } from '../services/CommonHelper';\n\n// Common Components\nimport TicketPageHeader from './common/TicketPageHeader';\nimport DashboardStats from './common/DashboardStats';\nimport DataTableCard from './common/DataTableCard';\nimport { Box, Container, Fade } from '@mui/material';\nimport { SupervisorAccount as SupervisorAccountIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MySpanTickets = () => {\n  _s();\n  const [stats, setStats] = useState({\n    NEWCASE: 0,\n    OPENCASE: 0,\n    TATCASE: 0,\n    Resolved: 0,\n    Closed: 0\n  });\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [source, setSource] = useState([]);\n  const [issueSubIssue, setIssueSubIssue] = useState([]);\n  const [statusList, setStatusList] = useState([]);\n  const [activeSearchType, setActiveSearchType] = useState(2);\n  const [fromDate, setFromDate] = useState(new Date());\n  const [toDate, setToDate] = useState(new Date());\n  const [ticketId, setTicketId] = useState('');\n  const [selected, setSelected] = useState({\n    Source: {\n      SourceID: 0,\n      Name: 'Select'\n    },\n    IssueType: undefined,\n    Status: undefined,\n    Product: {\n      ProductID: 0,\n      Name: 'Select'\n    }\n  });\n  const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));\n  const ProductOptions = [{\n    'ProductID': 0,\n    'Name': 'Select'\n  }, {\n    'ProductID': 115,\n    'Name': 'Investment'\n  }, {\n    'ProductID': 7,\n    'Name': 'Term'\n  }, {\n    'ProductID': 2,\n    'Name': 'Health'\n  }, {\n    'ProductID': 117,\n    'Name': 'Motor'\n  }];\n  useEffect(() => {\n    GetAllProcess();\n    GetDashboardCount(3);\n    getAllStatusMaster();\n    getAllIssueSubIssueService();\n  }, []);\n  const GetAllProcess = () => {\n    GetProcessMasterByAPI().then(data => {\n      if (data && data.length > 0) {\n        var _userDetails$EMPData$;\n        data.unshift({\n          Name: \"Select\",\n          SourceID: 0\n        });\n        setSource(data);\n        if ((userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$EMPData$ = userDetails.EMPData[0]) === null || _userDetails$EMPData$ === void 0 ? void 0 : _userDetails$EMPData$.ProcessID) > 0) {\n          setSelected(prev => ({\n            ...prev,\n            Source: {\n              SourceID: userDetails.EMPData[0].ProcessID\n            }\n          }));\n        }\n      } else setSource([]);\n    }).catch(() => setSource([]));\n  };\n  const GetDashboardCount = _type => {\n    GetSalesTicketCount({\n      type: _type\n    }).then(data => {\n      const newStats = {\n        NEWCASE: 0,\n        OPENCASE: 0,\n        TATCASE: 0,\n        Resolved: 0,\n        Closed: 0\n      };\n      data === null || data === void 0 ? void 0 : data.forEach(item => {\n        switch (item.StatusID) {\n          case 1:\n            newStats.NEWCASE = item.Count;\n            break;\n          case 2:\n            newStats.OPENCASE = item.Count;\n            break;\n          case 3:\n            newStats.Resolved = item.Count;\n            break;\n          case 4:\n            newStats.Closed = item.Count;\n            break;\n          case 5:\n            newStats.TATCASE = item.Count;\n            break;\n          default:\n            break;\n        }\n      });\n      setStats(newStats);\n    }).catch(() => setStats({\n      NEWCASE: 0,\n      OPENCASE: 0,\n      TATCASE: 0,\n      Resolved: 0,\n      Closed: 0\n    }));\n  };\n  const getAllIssueSubIssueService = () => {\n    GetAllIssueSubIssue().then(data => setIssueSubIssue(data || [])).catch(() => setIssueSubIssue([]));\n  };\n  const getAllStatusMaster = () => {\n    getStatusMaster().then(data => setStatusList(data || [])).catch(() => setStatusList([]));\n  };\n  const formatDateForRequest = (date, yearDuration = 0) => {\n    const d = new Date(date);\n    const year = d.getFullYear() - yearDuration;\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  };\n  const GetAgentTicketList = status => {\n    var _selected$Status, _userDetails$EMPData$2, _userDetails$EMPData$3, _userDetails$EMPData$4, _userDetails$EMPData$5, _selected$IssueType, _selected$Product;\n    const statusId = status !== 8 ? status : ((_selected$Status = selected.Status) === null || _selected$Status === void 0 ? void 0 : _selected$Status.StatusID) || 0;\n    let fromDateStr = formatDateForRequest(fromDate, 3);\n    let toDateStr = formatDateForRequest(toDate, 0);\n    if (status === 8) {\n      fromDateStr = formatDateForRequest(fromDate, 0);\n      toDateStr = formatDateForRequest(toDate, 0);\n    }\n    const obj = {\n      EmpID: (_userDetails$EMPData$2 = userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$EMPData$3 = userDetails.EMPData[0]) === null || _userDetails$EMPData$3 === void 0 ? void 0 : _userDetails$EMPData$3.EmpID) !== null && _userDetails$EMPData$2 !== void 0 ? _userDetails$EMPData$2 : 0,\n      FromDate: fromDateStr,\n      ToDate: toDateStr,\n      ProcessID: (_userDetails$EMPData$4 = userDetails === null || userDetails === void 0 ? void 0 : (_userDetails$EMPData$5 = userDetails.EMPData[0]) === null || _userDetails$EMPData$5 === void 0 ? void 0 : _userDetails$EMPData$5.ProcessID) !== null && _userDetails$EMPData$4 !== void 0 ? _userDetails$EMPData$4 : 0,\n      IssueID: ((_selected$IssueType = selected.IssueType) === null || _selected$IssueType === void 0 ? void 0 : _selected$IssueType.IssueID) || 0,\n      StatusID: statusId,\n      TicketID: 0,\n      TicketDisplayID: (ticketId === null || ticketId === void 0 ? void 0 : ticketId.trim()) || \"\",\n      ProductID: ((_selected$Product = selected.Product) === null || _selected$Product === void 0 ? void 0 : _selected$Product.ProductID) || 0\n    };\n    GetAdminTicketList(obj).then(data => {\n      const sorted = data !== null && data !== void 0 && data.length ? [...data].sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn)) : [];\n      setFeedbacks(sorted);\n    }).catch(() => setFeedbacks([]));\n  };\n  const exportData = () => {\n    if (typeof window !== 'undefined') window.XLSX = XLSX;\n    alasql.fn.datetime = function (dateStr) {\n      if (!dateStr) return '';\n      return formatDate(dateStr);\n    };\n    alasql('SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,' + 'CreatedByDetails -> EmployeeID as EmpID,' + 'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,' + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn' + ' INTO XLSX(\"Data_' + new Date().toDateString() + '.xlsx\", { headers: true }) FROM ? ', [feedbacks]);\n  };\n  const resetFilters = () => {\n    setFromDate(new Date());\n    setToDate(new Date());\n    setSelected({\n      Source: {\n        SourceID: 0,\n        Name: 'Select'\n      },\n      IssueType: undefined,\n      Status: undefined,\n      Product: {\n        ProductID: 0,\n        Name: 'Select'\n      }\n    });\n    setTicketId('');\n  };\n  const statCards = [{\n    label: 'New',\n    count: stats.NEWCASE,\n    id: 1,\n    className: 'new-status'\n  }, {\n    label: 'Open',\n    count: stats.OPENCASE,\n    id: 2,\n    className: 'open-status'\n  }, {\n    label: 'TAT Bust',\n    count: stats.TATCASE,\n    id: 5,\n    className: 'tat-status'\n  }, {\n    label: 'Resolved',\n    count: stats.Resolved,\n    id: 3,\n    className: 'resolved-status'\n  }, {\n    label: 'Closed',\n    count: stats.Closed,\n    id: 4,\n    className: 'closed-status'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"page-container\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      className: \"main-container\",\n      children: /*#__PURE__*/_jsxDEV(Fade, {\n        in: true,\n        timeout: 800,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(TicketPageHeader, {\n            title: \"My Process Tickets\",\n            subtitle: \"Manage tickets within your process area\",\n            icon: SupervisorAccountIcon,\n            activeSearchType: activeSearchType,\n            setActiveSearchType: setActiveSearchType,\n            resetFilters: resetFilters,\n            fromDate: fromDate,\n            setFromDate: setFromDate,\n            toDate: toDate,\n            setToDate: setToDate,\n            selected: selected,\n            setSelected: setSelected,\n            ticketId: ticketId,\n            setTicketId: setTicketId,\n            source: source,\n            issueSubIssue: issueSubIssue,\n            statusList: statusList,\n            ProductOptions: ProductOptions,\n            onSearch: () => GetAgentTicketList(8),\n            showProductField: true,\n            searchButtonText: \"Search Tickets\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this), activeSearchType === 2 && /*#__PURE__*/_jsxDEV(DashboardStats, {\n            statCards: statCards,\n            onStatClick: GetAgentTicketList\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(DataTableCard, {\n            feedbacks: feedbacks,\n            onExport: exportData,\n            tableType: 3,\n            redirectPage: \"/TicketDetails/\",\n            tableTitle: \"Process Ticket Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 9\n  }, this);\n};\n_s(MySpanTickets, \"f2gJ5aLdLGcKowWn1x22nesPnSA=\");\n_c = MySpanTickets;\nexport default MySpanTickets;\nvar _c;\n$RefreshReg$(_c, \"MySpanTickets\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "GetSalesTicketCount", "GetProcessMasterByAPI", "GetAllIssueSubIssue", "getStatusMaster", "GetAdminTicketList", "XLSX", "alasql", "formatDate", "Ticket<PERSON>ageHeader", "DashboardStats", "DataTableCard", "Box", "Container", "Fade", "SupervisorAccount", "SupervisorAccountIcon", "jsxDEV", "_jsxDEV", "MySpanTickets", "_s", "stats", "setStats", "NEWCASE", "OPENCASE", "TATCASE", "Resolved", "Closed", "feedbacks", "setFeedbacks", "source", "setSource", "issueSubIssue", "setIssueSubIssue", "statusList", "setStatusList", "activeSearchType", "setActiveSearchType", "fromDate", "setFromDate", "Date", "toDate", "setToDate", "ticketId", "setTicketId", "selected", "setSelected", "Source", "SourceID", "Name", "IssueType", "undefined", "Status", "Product", "ProductID", "userDetails", "JSON", "parse", "window", "localStorage", "getItem", "ProductOptions", "GetAllProcess", "GetDashboardCount", "getAllStatusMaster", "getAllIssueSubIssueService", "then", "data", "length", "_userDetails$EMPData$", "unshift", "EMPData", "ProcessID", "prev", "catch", "_type", "type", "newStats", "for<PERSON>ach", "item", "StatusID", "Count", "formatDateForRequest", "date", "yearDuration", "d", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "GetAgentTicketList", "status", "_selected$Status", "_userDetails$EMPData$2", "_userDetails$EMPData$3", "_userDetails$EMPData$4", "_userDetails$EMPData$5", "_selected$IssueType", "_selected$Product", "statusId", "fromDateStr", "toDateStr", "obj", "EmpID", "FromDate", "ToDate", "IssueID", "TicketID", "TicketDisplayID", "trim", "sorted", "sort", "a", "b", "CreatedOn", "exportData", "fn", "datetime", "dateStr", "toDateString", "resetFilters", "statCards", "label", "count", "id", "className", "children", "max<PERSON><PERSON><PERSON>", "in", "timeout", "title", "subtitle", "icon", "onSearch", "showProductField", "searchButtonText", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onStatClick", "onExport", "tableType", "redirectPage", "tableTitle", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/MySpanTickets.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n    GetSalesTicketCount,\r\n    GetProcessMasterByAPI,\r\n    GetAllIssueSubIssue,\r\n    getStatusMaster,\r\n    GetAdminTicketList\r\n} from '../services/feedbackService';\r\nimport '../styles/MyFeedback.css';\r\nimport '../styles/FeedbackStats.css';\r\nimport '../styles/MyAssignedTickets.css';\r\nimport * as XLSX from 'xlsx';\r\nimport alasql from 'alasql';\r\nimport { formatDate } from '../services/CommonHelper';\r\n\r\n// Common Components\r\nimport TicketPageHeader from './common/TicketPageHeader';\r\nimport DashboardStats from './common/DashboardStats';\r\nimport DataTableCard from './common/DataTableCard';\r\n\r\nimport {\r\n    Box,\r\n    Container,\r\n    Fade\r\n} from '@mui/material';\r\nimport {\r\n    SupervisorAccount as SupervisorAccountIcon\r\n} from '@mui/icons-material';\r\n\r\nconst MySpanTickets = () => {\r\n    const [stats, setStats] = useState({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });\r\n    const [feedbacks, setFeedbacks] = useState([]);\r\n    const [source, setSource] = useState([]);\r\n    const [issueSubIssue, setIssueSubIssue] = useState([]);\r\n    const [statusList, setStatusList] = useState([]);\r\n    const [activeSearchType, setActiveSearchType] = useState(2);\r\n    const [fromDate, setFromDate] = useState(new Date());\r\n    const [toDate, setToDate] = useState(new Date());\r\n    const [ticketId, setTicketId] = useState('');\r\n    const [selected, setSelected] = useState({\r\n        Source: { SourceID: 0, Name: 'Select' },\r\n        IssueType: undefined,\r\n        Status: undefined,\r\n        Product: { ProductID: 0, Name: 'Select' }\r\n    });\r\n\r\n    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));\r\n\r\n    const ProductOptions = [\r\n        { 'ProductID': 0, 'Name': 'Select' },\r\n        { 'ProductID': 115, 'Name': 'Investment' },\r\n        { 'ProductID': 7, 'Name': 'Term' },\r\n        { 'ProductID': 2, 'Name': 'Health' },\r\n        { 'ProductID': 117, 'Name': 'Motor' }\r\n    ];\r\n\r\n    useEffect(() => {\r\n        GetAllProcess();\r\n        GetDashboardCount(3);\r\n        getAllStatusMaster();\r\n        getAllIssueSubIssueService();\r\n    }, []);\r\n\r\n    const GetAllProcess = () => {\r\n        GetProcessMasterByAPI().then((data) => {\r\n            if (data && data.length > 0) {\r\n                data.unshift({ Name: \"Select\", SourceID: 0 });\r\n                setSource(data);\r\n                if (userDetails?.EMPData[0]?.ProcessID > 0) {\r\n                    setSelected(prev => ({\r\n                        ...prev,\r\n                        Source: { SourceID: userDetails.EMPData[0].ProcessID }\r\n                    }));\r\n                }\r\n            } else setSource([]);\r\n        }).catch(() => setSource([]));\r\n    };\r\n\r\n    const GetDashboardCount = (_type) => {\r\n        GetSalesTicketCount({ type: _type }).then((data) => {\r\n            const newStats = { NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 };\r\n            data?.forEach(item => {\r\n                switch (item.StatusID) {\r\n                    case 1: newStats.NEWCASE = item.Count; break;\r\n                    case 2: newStats.OPENCASE = item.Count; break;\r\n                    case 3: newStats.Resolved = item.Count; break;\r\n                    case 4: newStats.Closed = item.Count; break;\r\n                    case 5: newStats.TATCASE = item.Count; break;\r\n                    default: break;\r\n                }\r\n            });\r\n            setStats(newStats);\r\n        }).catch(() => setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 }));\r\n    };\r\n\r\n    const getAllIssueSubIssueService = () => {\r\n        GetAllIssueSubIssue().then(data => setIssueSubIssue(data || [])).catch(() => setIssueSubIssue([]));\r\n    };\r\n\r\n    const getAllStatusMaster = () => {\r\n        getStatusMaster().then(data => setStatusList(data || [])).catch(() => setStatusList([]));\r\n    };\r\n\r\n    const formatDateForRequest = (date, yearDuration = 0) => {\r\n        const d = new Date(date);\r\n        const year = d.getFullYear() - yearDuration;\r\n        const month = String(d.getMonth() + 1).padStart(2, '0');\r\n        const day = String(d.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n    };\r\n\r\n    const GetAgentTicketList = (status) => {\r\n        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;\r\n        let fromDateStr = formatDateForRequest(fromDate, 3);\r\n        let toDateStr = formatDateForRequest(toDate, 0);\r\n        if (status === 8) {\r\n            fromDateStr = formatDateForRequest(fromDate, 0);\r\n            toDateStr = formatDateForRequest(toDate, 0);\r\n        }\r\n        const obj = {\r\n            EmpID: userDetails?.EMPData[0]?.EmpID ?? 0,\r\n            FromDate: fromDateStr,\r\n            ToDate: toDateStr,\r\n            ProcessID: userDetails?.EMPData[0]?.ProcessID ?? 0,\r\n            IssueID: selected.IssueType?.IssueID || 0,\r\n            StatusID: statusId,\r\n            TicketID: 0,\r\n            TicketDisplayID: ticketId?.trim() || \"\",\r\n            ProductID: selected.Product?.ProductID || 0\r\n        };\r\n        GetAdminTicketList(obj).then(data => {\r\n            const sorted = data?.length ? [...data].sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn)) : [];\r\n            setFeedbacks(sorted);\r\n        }).catch(() => setFeedbacks([]));\r\n    };\r\n\r\n    const exportData = () => {\r\n        if (typeof window !== 'undefined') window.XLSX = XLSX;\r\n        alasql.fn.datetime = function (dateStr) {\r\n            if (!dateStr) return '';\r\n            return formatDate(dateStr);\r\n        };\r\n        alasql(\r\n            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,MatrixRole,BU,CreatedByDetails->Name as Name,' +\r\n            'CreatedByDetails -> EmployeeID as EmpID,' +\r\n            'AssignToDetails -> Name as AssignTo,AssignToDetails -> EmployeeID as AssignToEcode,' +\r\n            'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn' +\r\n            ' INTO XLSX(\"Data_' + new Date().toDateString() + '.xlsx\", { headers: true }) FROM ? ',\r\n            [feedbacks]\r\n        );\r\n    };\r\n\r\n    const resetFilters = () => {\r\n        setFromDate(new Date());\r\n        setToDate(new Date());\r\n        setSelected({\r\n            Source: { SourceID: 0, Name: 'Select' },\r\n            IssueType: undefined,\r\n            Status: undefined,\r\n            Product: { ProductID: 0, Name: 'Select' }\r\n        });\r\n        setTicketId('');\r\n    };\r\n\r\n    const statCards = [\r\n        { label: 'New', count: stats.NEWCASE, id: 1, className: 'new-status' },\r\n        { label: 'Open', count: stats.OPENCASE, id: 2, className: 'open-status' },\r\n        { label: 'TAT Bust', count: stats.TATCASE, id: 5, className: 'tat-status' },\r\n        { label: 'Resolved', count: stats.Resolved, id: 3, className: 'resolved-status' },\r\n        { label: 'Closed', count: stats.Closed, id: 4, className: 'closed-status' }\r\n    ];\r\n\r\n    return (\r\n        <Box className=\"page-container\">\r\n            <Container maxWidth=\"xl\" className=\"main-container\">\r\n                <Fade in timeout={800}>\r\n                    <Box>\r\n                        {/* Header and Search Form */}\r\n                        <TicketPageHeader\r\n                            title=\"My Process Tickets\"\r\n                            subtitle=\"Manage tickets within your process area\"\r\n                            icon={SupervisorAccountIcon}\r\n                            activeSearchType={activeSearchType}\r\n                            setActiveSearchType={setActiveSearchType}\r\n                            resetFilters={resetFilters}\r\n                            fromDate={fromDate}\r\n                            setFromDate={setFromDate}\r\n                            toDate={toDate}\r\n                            setToDate={setToDate}\r\n                            selected={selected}\r\n                            setSelected={setSelected}\r\n                            ticketId={ticketId}\r\n                            setTicketId={setTicketId}\r\n                            source={source}\r\n                            issueSubIssue={issueSubIssue}\r\n                            statusList={statusList}\r\n                            ProductOptions={ProductOptions}\r\n                            onSearch={() => GetAgentTicketList(8)}\r\n                            showProductField={true}\r\n                            searchButtonText=\"Search Tickets\"\r\n                        />\r\n\r\n                        {/* Dashboard Stats */}\r\n                        {activeSearchType === 2 && (\r\n                            <DashboardStats\r\n                                statCards={statCards}\r\n                                onStatClick={GetAgentTicketList}\r\n                            />\r\n                        )}\r\n\r\n\r\n                        {/* Data Table */}\r\n                        <DataTableCard\r\n                            feedbacks={feedbacks}\r\n                            onExport={exportData}\r\n                            tableType={3}\r\n                            redirectPage=\"/TicketDetails/\"\r\n                            tableTitle=\"Process Ticket Results\"\r\n                        />\r\n                    </Box>\r\n                </Fade>\r\n            </Container>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default MySpanTickets;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,mBAAmB,EACnBC,qBAAqB,EACrBC,mBAAmB,EACnBC,eAAe,EACfC,kBAAkB,QACf,6BAA6B;AACpC,OAAO,0BAA0B;AACjC,OAAO,6BAA6B;AACpC,OAAO,iCAAiC;AACxC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,QAAQ,0BAA0B;;AAErD;AACA,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,aAAa,MAAM,wBAAwB;AAElD,SACIC,GAAG,EACHC,SAAS,EACTC,IAAI,QACD,eAAe;AACtB,SACIC,iBAAiB,IAAIC,qBAAqB,QACvC,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC;IAAEwB,OAAO,EAAE,CAAC;IAAEC,QAAQ,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EACnG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,IAAIyC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,IAAIyC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC;IACrCgD,MAAM,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAS,CAAC;IACvCC,SAAS,EAAEC,SAAS;IACpBC,MAAM,EAAED,SAAS;IACjBE,OAAO,EAAE;MAAEC,SAAS,EAAE,CAAC;MAAEL,IAAI,EAAE;IAAS;EAC5C,CAAC,CAAC;EAEF,MAAMM,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;EAE1E,MAAMC,cAAc,GAAG,CACnB;IAAE,WAAW,EAAE,CAAC;IAAE,MAAM,EAAE;EAAS,CAAC,EACpC;IAAE,WAAW,EAAE,GAAG;IAAE,MAAM,EAAE;EAAa,CAAC,EAC1C;IAAE,WAAW,EAAE,CAAC;IAAE,MAAM,EAAE;EAAO,CAAC,EAClC;IAAE,WAAW,EAAE,CAAC;IAAE,MAAM,EAAE;EAAS,CAAC,EACpC;IAAE,WAAW,EAAE,GAAG;IAAE,MAAM,EAAE;EAAQ,CAAC,CACxC;EAED7D,SAAS,CAAC,MAAM;IACZ8D,aAAa,CAAC,CAAC;IACfC,iBAAiB,CAAC,CAAC,CAAC;IACpBC,kBAAkB,CAAC,CAAC;IACpBC,0BAA0B,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMH,aAAa,GAAGA,CAAA,KAAM;IACxB5D,qBAAqB,CAAC,CAAC,CAACgE,IAAI,CAAEC,IAAI,IAAK;MACnC,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAAA,IAAAC,qBAAA;QACzBF,IAAI,CAACG,OAAO,CAAC;UAAErB,IAAI,EAAE,QAAQ;UAAED,QAAQ,EAAE;QAAE,CAAC,CAAC;QAC7CjB,SAAS,CAACoC,IAAI,CAAC;QACf,IAAI,CAAAZ,WAAW,aAAXA,WAAW,wBAAAc,qBAAA,GAAXd,WAAW,CAAEgB,OAAO,CAAC,CAAC,CAAC,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBG,SAAS,IAAG,CAAC,EAAE;UACxC1B,WAAW,CAAC2B,IAAI,KAAK;YACjB,GAAGA,IAAI;YACP1B,MAAM,EAAE;cAAEC,QAAQ,EAAEO,WAAW,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACC;YAAU;UACzD,CAAC,CAAC,CAAC;QACP;MACJ,CAAC,MAAMzC,SAAS,CAAC,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC2C,KAAK,CAAC,MAAM3C,SAAS,CAAC,EAAE,CAAC,CAAC;EACjC,CAAC;EAED,MAAMgC,iBAAiB,GAAIY,KAAK,IAAK;IACjC1E,mBAAmB,CAAC;MAAE2E,IAAI,EAAED;IAAM,CAAC,CAAC,CAACT,IAAI,CAAEC,IAAI,IAAK;MAChD,MAAMU,QAAQ,GAAG;QAAEtD,OAAO,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAChFwC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,OAAO,CAACC,IAAI,IAAI;QAClB,QAAQA,IAAI,CAACC,QAAQ;UACjB,KAAK,CAAC;YAAEH,QAAQ,CAACtD,OAAO,GAAGwD,IAAI,CAACE,KAAK;YAAE;UACvC,KAAK,CAAC;YAAEJ,QAAQ,CAACrD,QAAQ,GAAGuD,IAAI,CAACE,KAAK;YAAE;UACxC,KAAK,CAAC;YAAEJ,QAAQ,CAACnD,QAAQ,GAAGqD,IAAI,CAACE,KAAK;YAAE;UACxC,KAAK,CAAC;YAAEJ,QAAQ,CAAClD,MAAM,GAAGoD,IAAI,CAACE,KAAK;YAAE;UACtC,KAAK,CAAC;YAAEJ,QAAQ,CAACpD,OAAO,GAAGsD,IAAI,CAACE,KAAK;YAAE;UACvC;YAAS;QACb;MACJ,CAAC,CAAC;MACF3D,QAAQ,CAACuD,QAAQ,CAAC;IACtB,CAAC,CAAC,CAACH,KAAK,CAAC,MAAMpD,QAAQ,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC,CAAC;EAC7F,CAAC;EAED,MAAMsC,0BAA0B,GAAGA,CAAA,KAAM;IACrC9D,mBAAmB,CAAC,CAAC,CAAC+D,IAAI,CAACC,IAAI,IAAIlC,gBAAgB,CAACkC,IAAI,IAAI,EAAE,CAAC,CAAC,CAACO,KAAK,CAAC,MAAMzC,gBAAgB,CAAC,EAAE,CAAC,CAAC;EACtG,CAAC;EAED,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;IAC7B5D,eAAe,CAAC,CAAC,CAAC8D,IAAI,CAACC,IAAI,IAAIhC,aAAa,CAACgC,IAAI,IAAI,EAAE,CAAC,CAAC,CAACO,KAAK,CAAC,MAAMvC,aAAa,CAAC,EAAE,CAAC,CAAC;EAC5F,CAAC;EAED,MAAM+C,oBAAoB,GAAGA,CAACC,IAAI,EAAEC,YAAY,GAAG,CAAC,KAAK;IACrD,MAAMC,CAAC,GAAG,IAAI7C,IAAI,CAAC2C,IAAI,CAAC;IACxB,MAAMG,IAAI,GAAGD,CAAC,CAACE,WAAW,CAAC,CAAC,GAAGH,YAAY;IAC3C,MAAMI,KAAK,GAAGC,MAAM,CAACJ,CAAC,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,GAAG,GAAGH,MAAM,CAACJ,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EACpC,CAAC;EAED,MAAME,kBAAkB,GAAIC,MAAM,IAAK;IAAA,IAAAC,gBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,iBAAA;IACnC,MAAMC,QAAQ,GAAGR,MAAM,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAAC,gBAAA,GAAAnD,QAAQ,CAACO,MAAM,cAAA4C,gBAAA,uBAAfA,gBAAA,CAAiBhB,QAAQ,KAAI,CAAC;IACvE,IAAIwB,WAAW,GAAGtB,oBAAoB,CAAC5C,QAAQ,EAAE,CAAC,CAAC;IACnD,IAAImE,SAAS,GAAGvB,oBAAoB,CAACzC,MAAM,EAAE,CAAC,CAAC;IAC/C,IAAIsD,MAAM,KAAK,CAAC,EAAE;MACdS,WAAW,GAAGtB,oBAAoB,CAAC5C,QAAQ,EAAE,CAAC,CAAC;MAC/CmE,SAAS,GAAGvB,oBAAoB,CAACzC,MAAM,EAAE,CAAC,CAAC;IAC/C;IACA,MAAMiE,GAAG,GAAG;MACRC,KAAK,GAAAV,sBAAA,GAAE1C,WAAW,aAAXA,WAAW,wBAAA2C,sBAAA,GAAX3C,WAAW,CAAEgB,OAAO,CAAC,CAAC,CAAC,cAAA2B,sBAAA,uBAAvBA,sBAAA,CAAyBS,KAAK,cAAAV,sBAAA,cAAAA,sBAAA,GAAI,CAAC;MAC1CW,QAAQ,EAAEJ,WAAW;MACrBK,MAAM,EAAEJ,SAAS;MACjBjC,SAAS,GAAA2B,sBAAA,GAAE5C,WAAW,aAAXA,WAAW,wBAAA6C,sBAAA,GAAX7C,WAAW,CAAEgB,OAAO,CAAC,CAAC,CAAC,cAAA6B,sBAAA,uBAAvBA,sBAAA,CAAyB5B,SAAS,cAAA2B,sBAAA,cAAAA,sBAAA,GAAI,CAAC;MAClDW,OAAO,EAAE,EAAAT,mBAAA,GAAAxD,QAAQ,CAACK,SAAS,cAAAmD,mBAAA,uBAAlBA,mBAAA,CAAoBS,OAAO,KAAI,CAAC;MACzC9B,QAAQ,EAAEuB,QAAQ;MAClBQ,QAAQ,EAAE,CAAC;MACXC,eAAe,EAAE,CAAArE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsE,IAAI,CAAC,CAAC,KAAI,EAAE;MACvC3D,SAAS,EAAE,EAAAgD,iBAAA,GAAAzD,QAAQ,CAACQ,OAAO,cAAAiD,iBAAA,uBAAhBA,iBAAA,CAAkBhD,SAAS,KAAI;IAC9C,CAAC;IACDjD,kBAAkB,CAACqG,GAAG,CAAC,CAACxC,IAAI,CAACC,IAAI,IAAI;MACjC,MAAM+C,MAAM,GAAG/C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,MAAM,GAAG,CAAC,GAAGD,IAAI,CAAC,CAACgD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI7E,IAAI,CAAC6E,CAAC,CAACC,SAAS,CAAC,GAAG,IAAI9E,IAAI,CAAC4E,CAAC,CAACE,SAAS,CAAC,CAAC,GAAG,EAAE;MAC1GzF,YAAY,CAACqF,MAAM,CAAC;IACxB,CAAC,CAAC,CAACxC,KAAK,CAAC,MAAM7C,YAAY,CAAC,EAAE,CAAC,CAAC;EACpC,CAAC;EAED,MAAM0F,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAI,OAAO7D,MAAM,KAAK,WAAW,EAAEA,MAAM,CAACpD,IAAI,GAAGA,IAAI;IACrDC,MAAM,CAACiH,EAAE,CAACC,QAAQ,GAAG,UAAUC,OAAO,EAAE;MACpC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MACvB,OAAOlH,UAAU,CAACkH,OAAO,CAAC;IAC9B,CAAC;IACDnH,MAAM,CACF,mHAAmH,GACnH,0CAA0C,GAC1C,qFAAqF,GACrF,gEAAgE,GAChE,mBAAmB,GAAG,IAAIiC,IAAI,CAAC,CAAC,CAACmF,YAAY,CAAC,CAAC,GAAG,oCAAoC,EACtF,CAAC/F,SAAS,CACd,CAAC;EACL,CAAC;EAED,MAAMgG,YAAY,GAAGA,CAAA,KAAM;IACvBrF,WAAW,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IACvBE,SAAS,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC;IACrBM,WAAW,CAAC;MACRC,MAAM,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAS,CAAC;MACvCC,SAAS,EAAEC,SAAS;MACpBC,MAAM,EAAED,SAAS;MACjBE,OAAO,EAAE;QAAEC,SAAS,EAAE,CAAC;QAAEL,IAAI,EAAE;MAAS;IAC5C,CAAC,CAAC;IACFL,WAAW,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMiF,SAAS,GAAG,CACd;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE1G,KAAK,CAACE,OAAO;IAAEyG,EAAE,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,EACtE;IAAEH,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE1G,KAAK,CAACG,QAAQ;IAAEwG,EAAE,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAc,CAAC,EACzE;IAAEH,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE1G,KAAK,CAACI,OAAO;IAAEuG,EAAE,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC3E;IAAEH,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE1G,KAAK,CAACK,QAAQ;IAAEsG,EAAE,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAkB,CAAC,EACjF;IAAEH,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE1G,KAAK,CAACM,MAAM;IAAEqG,EAAE,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAgB,CAAC,CAC9E;EAED,oBACI/G,OAAA,CAACN,GAAG;IAACqH,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC3BhH,OAAA,CAACL,SAAS;MAACsH,QAAQ,EAAC,IAAI;MAACF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC/ChH,OAAA,CAACJ,IAAI;QAACsH,EAAE;QAACC,OAAO,EAAE,GAAI;QAAAH,QAAA,eAClBhH,OAAA,CAACN,GAAG;UAAAsH,QAAA,gBAEAhH,OAAA,CAACT,gBAAgB;YACb6H,KAAK,EAAC,oBAAoB;YAC1BC,QAAQ,EAAC,yCAAyC;YAClDC,IAAI,EAAExH,qBAAsB;YAC5BoB,gBAAgB,EAAEA,gBAAiB;YACnCC,mBAAmB,EAAEA,mBAAoB;YACzCuF,YAAY,EAAEA,YAAa;YAC3BtF,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBE,MAAM,EAAEA,MAAO;YACfC,SAAS,EAAEA,SAAU;YACrBG,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBH,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBd,MAAM,EAAEA,MAAO;YACfE,aAAa,EAAEA,aAAc;YAC7BE,UAAU,EAAEA,UAAW;YACvB2B,cAAc,EAAEA,cAAe;YAC/B4E,QAAQ,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC,CAAC,CAAE;YACtC4C,gBAAgB,EAAE,IAAK;YACvBC,gBAAgB,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,EAGD3G,gBAAgB,KAAK,CAAC,iBACnBlB,OAAA,CAACR,cAAc;YACXmH,SAAS,EAAEA,SAAU;YACrBmB,WAAW,EAAElD;UAAmB;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACJ,eAID7H,OAAA,CAACP,aAAa;YACViB,SAAS,EAAEA,SAAU;YACrBqH,QAAQ,EAAE1B,UAAW;YACrB2B,SAAS,EAAE,CAAE;YACbC,YAAY,EAAC,iBAAiB;YAC9BC,UAAU,EAAC;UAAwB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEd,CAAC;AAAC3H,EAAA,CAnMID,aAAa;AAAAkI,EAAA,GAAblI,aAAa;AAqMnB,eAAeA,aAAa;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}